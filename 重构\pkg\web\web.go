package web

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	"cursor-manager/pkg/config"
	"cursor-manager/pkg/email"
	"cursor-manager/pkg/utils"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
)

const (
	MaxWaitSeconds = 90 // 等待90秒
)

// WebAutomation 网页自动化结构体
type WebAutomation struct {
	browser *rod.Browser
	page    *rod.Page
	config  *config.Config
}

// NewWebAutomation 创建新的网页自动化实例
func NewWebAutomation() (*WebAutomation, error) {
	cfg, err := config.GetConfig()
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	return &WebAutomation{
		config: cfg,
	}, nil
}

// GetRandomWaitTime 从配置中获取一个随机的等待时间，以模仿人类操作
func (w *WebAutomation) GetRandomWaitTime(timingType string) time.Duration {
	if w.config == nil {
		return time.Duration(rand.Float64()*1000+500) * time.Millisecond
	}

	timingValue := w.config.Get("Timing", timingType)
	return utils.GetRandomWaitTime(timingValue)
}

// GenerateRandomEmail 根据配置中的前缀生成随机邮箱
func GenerateRandomEmail(statusCallback func(string)) string {
	cfg, err := config.GetConfig()
	if err != nil {
		if statusCallback != nil {
			statusCallback("❌ 无法加载配置，无法生成邮箱。")
		}
		return ""
	}

	prefix := cfg.GetWithFallback("Email", "prefix", "")
	if prefix == "" {
		if statusCallback != nil {
			statusCallback("❌ 邮箱前缀未设置，无法生成邮箱。请在GUI中设置。")
		}
		return ""
	}

	randomPart := utils.RandomString(7)
	email := fmt.Sprintf("%<EMAIL>", prefix, strings.ToLower(randomPart))
	return email
}

// StartBrowser 启动浏览器
func (w *WebAutomation) StartBrowser() error {
	// 获取浏览器路径
	chromePath := w.config.GetWithFallback("Browser", "chrome_path", "")
	
	var l *launcher.Launcher
	if chromePath != "" && utils.FileExists(chromePath) {
		l = launcher.New().Bin(chromePath)
	} else {
		l = launcher.New()
	}

	// 设置无痕模式
	l = l.Set("incognito")

	// 启动浏览器
	url := l.MustLaunch()
	w.browser = rod.New().ControlURL(url).MustConnect()

	return nil
}

// CloseBrowser 关闭浏览器
func (w *WebAutomation) CloseBrowser() {
	if w.browser != nil {
		w.browser.MustClose()
	}
}

// WaitForVerificationCode 轮询等待验证码
func WaitForVerificationCode(statusCallback func(string), codeChannel <-chan string) string {
	statusCallback("正在等待接收验证码 (最长90秒)...")
	
	timeout := time.After(MaxWaitSeconds * time.Second)
	ticker := time.NewTicker(15 * time.Second)
	defer ticker.Stop()

	for i := 0; i < MaxWaitSeconds; i++ {
		select {
		case code := <-codeChannel:
			if code != "" {
				statusCallback(fmt.Sprintf("COPY_AND_SHOW:%s", code))
				return code
			}
		case <-ticker.C:
			statusCallback(fmt.Sprintf("已等待 %d 秒，接收中...", i+1))
		case <-timeout:
			statusCallback("等待验证码超时。")
			return ""
		default:
			time.Sleep(1 * time.Second)
		}
	}

	statusCallback("等待验证码超时。")
	return ""
}

// AutomateLogin 使用Rod进行自动化登录
func (w *WebAutomation) AutomateLogin(url, emailAddr string, statusCallback func(string), codeChannel <-chan string) error {
	if w.browser == nil {
		return fmt.Errorf("浏览器未启动")
	}

	// 创建新页面
	w.page = w.browser.MustPage()
	defer w.page.MustClose()

	statusCallback(fmt.Sprintf("正在加载URL: %s", url))
	w.page.MustNavigate(url)

	// 步骤 1: 填写邮箱并点击 "Continue"
	statusCallback("步骤 1: 正在填写邮箱并点击 'Continue'...")
	
	emailInput := w.page.MustElement(`input[name="email"]`)
	emailInput.MustInput(emailAddr)
	
	continueButton := w.page.MustElement(`input[type="submit"]`)
	continueButton.MustClick()
	
	statusCallback("已点击 'Continue'，等待跳转...")

	// 等待页面加载
	time.Sleep(w.GetRandomWaitTime("page_load_wait"))

	// 步骤 2: 在下一页点击 "Email sign-in code"
	statusCallback("步骤 2: 等待密码页面加载，准备点击验证码登录...")
	
	// 等待并点击验证码登录按钮
	signInCodeButton, err := w.page.Timeout(15 * time.Second).Element(`text="Email sign-in code"`)
	if err != nil {
		return fmt.Errorf("未找到验证码登录按钮: %v", err)
	}
	
	signInCodeButton.MustClick()
	statusCallback("已点击 'Email sign-in code' 按钮。")

	// 步骤 3: 等待验证码
	statusCallback("步骤 3: 启动验证码监听程序...")
	code := WaitForVerificationCode(statusCallback, codeChannel)

	if code == "" {
		finalMessage := "\n❌ 错误: 等待验证码超时。请手动输入验证码完成登录。\n\n注意：操作完成后，请您手动关闭此浏览器窗口。"
		statusCallback(finalMessage)
		return fmt.Errorf("验证码超时")
	}

	// 步骤 4: 自动填写验证码
	statusCallback("步骤 4: 成功获取验证码，正在自动填写...")
	
	for i, digit := range code {
		selector := fmt.Sprintf(`input[data-index="%d"]`, i)
		inputBox, err := w.page.Element(selector)
		if err != nil {
			return fmt.Errorf("未找到验证码输入框 %d: %v", i, err)
		}
		
		inputBox.MustInput(string(digit))
		time.Sleep(time.Duration(rand.Float64()*200+100) * time.Millisecond)
	}

	statusCallback("✅ 验证码填写完成。")
	finalMessage := "\n✅ 成功: 验证码已自动填写。\n\n如果页面没有自动跳转，请手动完成最后步骤。完成后请手动关闭此浏览器窗口。"
	statusCallback(finalMessage)

	return nil
}

// RunAutoLoginFlow 执行完整的自动登录流程
func RunAutoLoginFlow(monitoringEmail, loginEmail, password string, statusCallback func(string)) error {
	if password == "" {
		statusCallback("❌ 错误: 邮箱密码未设置，请在凭据中设置并保存。")
		return fmt.Errorf("邮箱密码未设置")
	}

	if monitoringEmail == "" || !strings.Contains(monitoringEmail, "@") {
		statusCallback("❌ 错误: 用于监控的邮箱无效。")
		return fmt.Errorf("监控邮箱无效")
	}

	// 启动邮箱监控
	statusCallback("正在启动邮箱监控...")
	codeChannel := make(chan string, 1)
	
	// 启动邮件客户端
	go func() {
		client := email.NewEmailClient(monitoringEmail, password, func(msg string) {
			if strings.HasPrefix(msg, "VERIFICATION_CODE:") {
				code := strings.TrimPrefix(msg, "VERIFICATION_CODE:")
				select {
				case codeChannel <- code:
				default:
				}
			}
			statusCallback(fmt.Sprintf("[邮箱客户端]: %s", msg))
		})
		
		if err := client.MonitorEmails(); err != nil {
			statusCallback(fmt.Sprintf("邮件监控出错: %v", err))
		}
	}()

	statusCallback(fmt.Sprintf("邮箱监控已在后台启动，监控账号: %s", monitoringEmail))

	// 这里需要获取当前浏览器URL，暂时使用占位符
	// 在实际实现中，可能需要通过其他方式获取当前活动的浏览器标签页URL
	targetURL := "https://cursor.sh/sign-in" // 占位符URL
	statusCallback("正在获取当前Chrome浏览器URL...")
	statusCallback(fmt.Sprintf("成功获取URL: %s", targetURL))

	// 创建网页自动化实例
	webAuto, err := NewWebAutomation()
	if err != nil {
		return fmt.Errorf("创建网页自动化实例失败: %v", err)
	}

	// 启动浏览器
	if err := webAuto.StartBrowser(); err != nil {
		return fmt.Errorf("启动浏览器失败: %v", err)
	}
	defer webAuto.CloseBrowser()

	statusCallback("正在打开新的浏览器窗口...")
	statusCallback(fmt.Sprintf("使用随机邮箱进行登录: %s", loginEmail))

	// 执行自动登录
	return webAuto.AutomateLogin(targetURL, loginEmail, statusCallback, codeChannel)
}
