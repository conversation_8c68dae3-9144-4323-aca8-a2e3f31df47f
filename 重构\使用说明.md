# Cursor管理工具 - 使用说明

## 🚀 快速开始

### 1. 构建程序
```cmd
build.bat
```

### 2. 运行程序
双击 `build\cursor-manager.exe` 或在命令行运行：
```cmd
.\build\cursor-manager.exe
```

## 📋 主要功能

### 1. 一键登录
- **功能**: 自动完成Cursor登录流程
- **前提**: 需要先配置邮箱前缀和密码
- **流程**: 
  1. 生成随机邮箱
  2. 启动浏览器自动化
  3. 监控邮箱验证码
  4. 自动填写验证码

### 2. 一键重置环境
- **功能**: 重置Cursor的机器ID
- **操作**: 
  1. 关闭所有Cursor进程
  2. 生成新的机器ID
  3. 更新配置文件
  4. 重启Cursor

### 3. 邮箱客户端
- **功能**: 监控指定邮箱的验证码
- **用途**: 独立的验证码接收工具
- **显示**: 收到验证码时会弹出消息框

### 4. 生成随机邮箱
- **功能**: 基于配置的前缀生成随机邮箱
- **格式**: `前缀+随机字符@2925.com`
- **用途**: 用于注册新账号

### 5. 配置设置
- **功能**: 查看当前配置信息
- **内容**: 邮箱前缀、密码状态
- **注意**: 此版本暂不支持在线修改

## ⚙️ 配置文件

配置文件位置：`文档\\.cursor-free-vip\\config.ini`

### 主要配置项：

```ini
[Email]
prefix = 你的邮箱前缀
password = 你的邮箱密码

[Browser]
chrome_path = Chrome浏览器路径

[Timing]
# 各种等待时间配置
min_random_time = 0.1
max_random_time = 0.8
```

### 手动配置方法：

1. 运行程序一次（会自动创建配置文件）
2. 编辑配置文件，设置邮箱前缀和密码
3. 重新运行程序

## 🔧 故障排除

### 问题1: 程序无法启动
**可能原因**: 
- 不是Windows系统
- 缺少必要的系统文件

**解决方案**: 
- 确保在Windows 7+系统上运行
- 尝试以管理员身份运行

### 问题2: 一键登录失败
**可能原因**: 
- 邮箱配置不正确
- Chrome浏览器未安装
- 网络连接问题

**解决方案**: 
- 检查邮箱前缀和密码配置
- 安装Chrome浏览器
- 检查网络连接

### 问题3: 环境重置失败
**可能原因**: 
- 权限不足
- Cursor进程无法关闭

**解决方案**: 
- 以管理员身份运行程序
- 手动关闭Cursor进程后重试

### 问题4: 邮箱监控无响应
**可能原因**: 
- 邮箱服务器连接失败
- 邮箱密码错误

**解决方案**: 
- 检查网络连接
- 验证邮箱密码是否正确

## 💡 使用技巧

1. **首次使用**: 建议先配置邮箱信息，然后测试邮箱客户端功能
2. **批量操作**: 可以多次运行程序进行批量账号操作
3. **安全性**: 程序不会保存敏感信息到日志文件
4. **效率**: 建议在网络状况良好时使用自动化功能

## 📞 技术支持

如果遇到问题：
1. 检查配置文件是否正确
2. 确认系统环境符合要求
3. 查看错误消息框的具体提示
4. 尝试重新构建程序

## 🔄 版本特点

- ✅ **纯exe文件**: 无需安装，双击即用
- ✅ **无终端窗口**: 完全静默运行
- ✅ **原生界面**: Windows原生对话框
- ✅ **零依赖**: 运行时无需任何额外组件
- ✅ **轻量级**: 文件小，启动快
