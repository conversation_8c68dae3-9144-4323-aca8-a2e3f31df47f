package main

import (
	"fmt"
	"log"
	"os"

	"cursor-manager/pkg/app"
	"cursor-manager/pkg/config"
	"cursor-manager/pkg/email"
	"cursor-manager/pkg/utils"
	"cursor-manager/pkg/web"
)

func main() {
	fmt.Println("Cursor管理工具 - Go版本 (命令行版)")
	fmt.Println("================================")

	if len(os.Args) < 2 {
		showHelp()
		return
	}

	command := os.Args[1]

	switch command {
	case "config":
		handleConfig()
	case "reset":
		handleReset()
	case "email":
		handleEmail()
	case "generate-email":
		handleGenerateEmail()
	case "test":
		handleTest()
	default:
		fmt.Printf("未知命令: %s\n", command)
		showHelp()
	}
}

func showHelp() {
	fmt.Println("可用命令:")
	fmt.Println("  config         - 显示配置信息")
	fmt.Println("  reset          - 重置Cursor环境")
	fmt.Println("  email <email> <password> - 监控邮箱")
	fmt.Println("  generate-email - 生成随机邮箱")
	fmt.Println("  test           - 测试所有模块")
}

func handleConfig() {
	cfg, err := config.GetConfig()
	if err != nil {
		log.Fatalf("获取配置失败: %v", err)
	}

	cfg.PrintConfig()
}

func handleReset() {
	fmt.Println("开始重置Cursor环境...")
	
	statusCallback := func(msg string) {
		fmt.Println(msg)
	}

	if err := app.RunFullResetFlow(statusCallback); err != nil {
		log.Fatalf("重置失败: %v", err)
	}

	fmt.Println("重置完成！")
}

func handleEmail() {
	if len(os.Args) < 4 {
		fmt.Println("用法: cursor-manager email <邮箱> <密码>")
		return
	}

	emailAddr := os.Args[2]
	password := os.Args[3]

	fmt.Printf("开始监控邮箱: %s\n", emailAddr)

	statusCallback := func(msg string) {
		fmt.Println(msg)
	}

	client := email.NewEmailClient(emailAddr, password, statusCallback)
	if err := client.MonitorEmails(); err != nil {
		log.Fatalf("邮箱监控失败: %v", err)
	}
}

func handleGenerateEmail() {
	statusCallback := func(msg string) {
		fmt.Println(msg)
	}

	email := web.GenerateRandomEmail(statusCallback)
	if email != "" {
		fmt.Printf("生成的随机邮箱: %s\n", email)
	} else {
		fmt.Println("生成邮箱失败，请检查配置")
	}
}

func handleTest() {
	fmt.Println("测试所有模块...")

	// 测试配置模块
	fmt.Println("\n1. 测试配置模块...")
	cfg, err := config.GetConfig()
	if err != nil {
		fmt.Printf("❌ 配置模块测试失败: %v\n", err)
	} else {
		fmt.Println("✅ 配置模块测试通过")
	}

	// 测试工具模块
	fmt.Println("\n2. 测试工具模块...")
	randomStr := utils.RandomString(10)
	if len(randomStr) == 10 {
		fmt.Printf("✅ 工具模块测试通过 (随机字符串: %s)\n", randomStr)
	} else {
		fmt.Println("❌ 工具模块测试失败")
	}

	// 测试应用管理模块
	fmt.Println("\n3. 测试应用管理模块...")
	paths := app.GetCursorPaths()
	if paths != nil {
		fmt.Printf("✅ 应用管理模块测试通过 (DB路径: %s)\n", paths.DBPath)
	} else {
		fmt.Println("❌ 应用管理模块测试失败")
	}

	// 测试邮箱生成
	fmt.Println("\n4. 测试邮箱生成...")
	if cfg != nil {
		cfg.Set("Email", "prefix", "test")
		email := web.GenerateRandomEmail(nil)
		if email != "" {
			fmt.Printf("✅ 邮箱生成测试通过 (邮箱: %s)\n", email)
		} else {
			fmt.Println("❌ 邮箱生成测试失败")
		}
	}

	fmt.Println("\n测试完成！")
}
