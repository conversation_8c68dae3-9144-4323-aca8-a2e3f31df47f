package utils

import (
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"
)

// EMOJI 表情符号常量
var EMOJI = map[string]string{
	"INFO":    "ℹ️ ",
	"ERROR":   "❌ ",
	"SUCCESS": "✅ ",
	"WARNING": "⚠️ ",
	"LOGIN":   "🔑 ",
}

// GetUserDocumentsPath 获取用户文档路径
func GetUserDocumentsPath() (string, error) {
	switch runtime.GOOS {
	case "windows":
		// Windows: 使用USERPROFILE环境变量
		userProfile := os.Getenv("USERPROFILE")
		if userProfile != "" {
			return filepath.Join(userProfile, "Documents"), nil
		}
		// 回退方案
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		return filepath.Join(homeDir, "Documents"), nil

	case "darwin":
		// macOS: 使用用户主目录下的Documents
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		return filepath.Join(homeDir, "Documents"), nil

	case "linux":
		// Linux: 使用用户主目录下的Documents
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		return filepath.Join(homeDir, "Documents"), nil

	default:
		// 其他系统：使用用户主目录
		return os.UserHomeDir()
	}
}

// GetCurrentChromeURL 获取当前Chrome浏览器的URL
// 注意：Go版本中这个功能需要使用不同的方法实现
// 可能需要使用浏览器自动化工具或系统API
func GetCurrentChromeURL() (string, error) {
	// 这是一个占位符实现
	// 在实际使用中，可能需要使用rod或其他浏览器自动化工具
	// 或者通过系统API获取活动窗口信息
	return "", fmt.Errorf("GetCurrentChromeURL 功能需要在具体使用时实现")
}

// GetDefaultDriverPath 获取默认驱动路径
func GetDefaultDriverPath(browserType string) string {
	return GetDefaultChromeDriverPath()
}

// GetDefaultChromeDriverPath 获取默认Chrome驱动路径
func GetDefaultChromeDriverPath() string {
	switch runtime.GOOS {
	case "windows":
		return filepath.Join(".", "drivers", "chromedriver.exe")
	case "darwin":
		return filepath.Join(".", "drivers", "chromedriver")
	default: // linux
		return "/usr/local/bin/chromedriver"
	}
}

// GetDefaultBrowserPath 获取默认浏览器可执行文件路径
func GetDefaultBrowserPath(browserType string) string {
	switch runtime.GOOS {
	case "windows":
		return getWindowsChromePath()
	case "darwin":
		return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
	default: // linux
		return getLinuxChromePath()
	}
}

// getWindowsChromePath 获取Windows Chrome路径
func getWindowsChromePath() string {
	// 1. 尝试从PATH环境变量查找
	if path, err := exec.LookPath("chrome.exe"); err == nil {
		return path
	}

	// 2. 尝试常见的安装路径
	possiblePaths := []string{
		filepath.Join(os.Getenv("LOCALAPPDATA"), "Google", "Chrome", "Application", "chrome.exe"),
		filepath.Join(os.Getenv("PROGRAMFILES"), "Google", "Chrome", "Application", "chrome.exe"),
		filepath.Join(os.Getenv("PROGRAMFILES(X86)"), "Google", "Chrome", "Application", "chrome.exe"),
		`C:\Program Files\Google\Chrome\Application\chrome.exe`,
		`C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`,
	}

	for _, path := range possiblePaths {
		if path != "" {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
	}

	// 3. 回退到默认路径
	return `C:\Program Files\Google\Chrome\Application\chrome.exe`
}

// getLinuxChromePath 获取Linux Chrome路径
func getLinuxChromePath() string {
	// 尝试从PATH环境变量查找
	possibleNames := []string{"chrome", "google-chrome", "chromium", "chromium-browser"}

	for _, name := range possibleNames {
		if path, err := exec.LookPath(name); err == nil {
			return path
		}
	}

	// 回退到常见路径
	return "/usr/bin/google-chrome"
}

// GetLinuxCursorPath 获取Linux Cursor路径
func GetLinuxCursorPath() string {
	possiblePaths := []string{
		"/opt/Cursor/resources/app",
		"/usr/share/cursor/resources/app",
		"/opt/cursor-bin/resources/app",
		"/usr/lib/cursor/resources/app",
	}

	homeDir, _ := os.UserHomeDir()
	if homeDir != "" {
		possiblePaths = append(possiblePaths, filepath.Join(homeDir, ".local", "share", "cursor", "resources", "app"))
	}

	// 返回第一个存在的路径
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	// 如果都不存在，返回第一个作为默认值
	return possiblePaths[0]
}

// GetRandomWaitTime 根据配置获取随机等待时间
func GetRandomWaitTime(timingValue string) time.Duration {
	if timingValue == "" {
		// 默认0.5-1.5秒
		return time.Duration(rand.Float64()*1000+500) * time.Millisecond
	}

	var minTime, maxTime float64

	// 检查是否是范围值 (例如 "0.5-1.5" 或 "0.5,1.5")
	if strings.Contains(timingValue, "-") {
		parts := strings.Split(timingValue, "-")
		if len(parts) == 2 {
			if min, err := strconv.ParseFloat(strings.TrimSpace(parts[0]), 64); err == nil {
				minTime = min
			}
			if max, err := strconv.ParseFloat(strings.TrimSpace(parts[1]), 64); err == nil {
				maxTime = max
			}
		}
	} else if strings.Contains(timingValue, ",") {
		parts := strings.Split(timingValue, ",")
		if len(parts) == 2 {
			if min, err := strconv.ParseFloat(strings.TrimSpace(parts[0]), 64); err == nil {
				minTime = min
			}
			if max, err := strconv.ParseFloat(strings.TrimSpace(parts[1]), 64); err == nil {
				maxTime = max
			}
		}
	} else {
		// 单个值，作为最小和最大值
		if val, err := strconv.ParseFloat(strings.TrimSpace(timingValue), 64); err == nil {
			minTime = val
			maxTime = val
		}
	}

	// 如果解析失败，使用默认值
	if minTime == 0 && maxTime == 0 {
		minTime = 0.5
		maxTime = 1.5
	}

	// 确保最小值不大于最大值
	if minTime > maxTime {
		minTime, maxTime = maxTime, minTime
	}

	// 生成随机时间
	randomTime := minTime + rand.Float64()*(maxTime-minTime)
	return time.Duration(randomTime * float64(time.Second))
}

// FileExists 检查文件是否存在
func FileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// DirExists 检查目录是否存在
func DirExists(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		return false
	}
	return info.IsDir()
}

// EnsureDir 确保目录存在，如果不存在则创建
func EnsureDir(path string) error {
	if !DirExists(path) {
		return os.MkdirAll(path, 0755)
	}
	return nil
}

// RandomString 生成指定长度的随机字符串
func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// RandomNumber 生成指定长度的随机数字字符串
func RandomNumber(length int) string {
	const charset = "0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// init 初始化随机数种子
func init() {
	rand.Seed(time.Now().UnixNano())
}
