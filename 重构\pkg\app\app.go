package app

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"cursor-manager/pkg/utils"

	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
)

// CursorPaths Cursor相关路径结构
type CursorPaths struct {
	DBPath        string
	MachineIDPath string
	AppPath       string
}

// StatusCallback 状态回调函数类型
type StatusCallback func(string)

// MachineIDResetter 机器ID重置器
type MachineIDResetter struct {
	system         string
	paths          *CursorPaths
	statusCallback StatusCallback
}

// NewMachineIDResetter 创建新的机器ID重置器
func NewMachineIDResetter(statusCallback StatusCallback) *MachineIDResetter {
	paths := GetCursorPaths()
	if paths == nil {
		statusCallback("❌ 错误: 无法获取Cursor路径，重置操作无法继续。")
		return nil
	}

	return &MachineIDResetter{
		system:         runtime.GOOS,
		paths:          paths,
		statusCallback: statusCallback,
	}
}

// GetCursorPaths 获取Cursor相关文件的路径
func GetCursorPaths() *CursorPaths {
	system := runtime.GOOS
	var basePath, appPath string

	switch system {
	case "windows":
		appdata := os.Getenv("APPDATA")
		if appdata == "" {
			return nil
		}
		basePath = filepath.Join(appdata, "Cursor")

		// 定义Cursor在Windows上的潜在安装路径
		localAppData := os.Getenv("LOCALAPPDATA")
		programFiles := os.Getenv("PROGRAMFILES")

		appPathUser := filepath.Join(localAppData, "Programs", "Cursor", "Cursor.exe")
		appPathSystem := filepath.Join(programFiles, "cursor", "Cursor.exe")

		// 检查可执行文件，优先选择用户特定路径
		if utils.FileExists(appPathUser) {
			appPath = appPathUser
		} else if utils.FileExists(appPathSystem) {
			appPath = appPathSystem
		} else {
			// 回退到用户路径
			appPath = appPathUser
		}

	case "darwin":
		homeDir, _ := os.UserHomeDir()
		basePath = filepath.Join(homeDir, "Library", "Application Support", "Cursor")
		appPath = "/Applications/Cursor.app"

	case "linux":
		homeDir, _ := os.UserHomeDir()
		basePath = filepath.Join(homeDir, ".config", "cursor")
		appPath = "/usr/bin/cursor"
		if !utils.FileExists(appPath) {
			appPath = filepath.Join(homeDir, "Applications", "Cursor.AppImage")
		}

	default:
		return nil
	}

	dbPath := filepath.Join(basePath, "User", "globalStorage", "state.vscdb")
	machineIDFilename := "machineId"
	if system == "linux" {
		machineIDFilename = "machineid"
	}
	machineIDPath := filepath.Join(basePath, machineIDFilename)

	return &CursorPaths{
		DBPath:        dbPath,
		MachineIDPath: machineIDPath,
		AppPath:       appPath,
	}
}

// GenerateNewIDs 生成新的机器ID
func (r *MachineIDResetter) GenerateNewIDs() map[string]string {
	r.statusCallback("ℹ️ 信息: 正在生成新的机器ID...")

	newIDs := map[string]string{
		"dev_device_id":       uuid.New().String(),
		"vscode_machine_id":   fmt.Sprintf("vscode-machine-id-%s", uuid.New().String()),
		"vscode_telemetry_id": fmt.Sprintf("vscode-telemetry-machine-id-%s", uuid.New().String()),
		"telemetry_device_id": strings.ReplaceAll(uuid.New().String(), "-", ""),
	}

	r.statusCallback("✅ 成功: 新的机器ID已生成。")
	return newIDs
}

// UpdateSQLiteDB 更新SQLite数据库中的ID
func (r *MachineIDResetter) UpdateSQLiteDB(newIDs map[string]string) error {
	if !utils.FileExists(r.paths.DBPath) {
		r.statusCallback(fmt.Sprintf("⚠️ 警告: 未找到SQLite数据库，跳过更新: %s", r.paths.DBPath))
		return nil
	}

	r.statusCallback(fmt.Sprintf("ℹ️ 信息: 正在更新SQLite数据库: %s", r.paths.DBPath))

	// 定义要更新的键值对
	telemetryData := map[string]string{"id": newIDs["vscode_telemetry_id"]}
	telemetryJSON, _ := json.Marshal(telemetryData)

	itemsToUpdate := map[string]string{
		"telemetry.machineId": string(telemetryJSON),
		"storage.machineId":   newIDs["vscode_machine_id"],
		"telemetry.deviceId":  newIDs["telemetry_device_id"],
	}

	db, err := sql.Open("sqlite3", r.paths.DBPath)
	if err != nil {
		return fmt.Errorf("打开SQLite数据库失败: %v", err)
	}
	defer db.Close()

	for key, value := range itemsToUpdate {
		// 尝试更新
		result, err := db.Exec("UPDATE ItemTable SET value = ? WHERE key = ?", value, key)
		if err != nil {
			return fmt.Errorf("更新键 '%s' 失败: %v", key, err)
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			// 如果没有行被更新，则插入
			r.statusCallback(fmt.Sprintf("ℹ️ 信息: 键 '%s' 不存在，正在插入...", key))
			_, err = db.Exec("INSERT INTO ItemTable (key, value) VALUES (?, ?)", key, value)
			if err != nil {
				return fmt.Errorf("插入键 '%s' 失败: %v", key, err)
			}
		}
	}

	r.statusCallback("✅ 成功: SQLite数据库更新成功。")
	return nil
}

// UpdateSystemSpecificIDs 更新特定于操作系统的ID
func (r *MachineIDResetter) UpdateSystemSpecificIDs() {
	switch r.system {
	case "windows":
		r.updateWindowsMachineGUID()
	case "darwin":
		r.updateMacOSPlatformUUID()
	}
}

// updateWindowsMachineGUID 更新Windows注册表中的MachineGuid
func (r *MachineIDResetter) updateWindowsMachineGUID() {
	r.statusCallback("ℹ️ 信息: 正在更新Windows注册表中的MachineGuid...")

	// 在Go中更新Windows注册表需要使用golang.org/x/sys/windows/registry
	// 这里提供一个基本实现框架
	r.statusCallback("⚠️ 警告: Windows注册表更新需要管理员权限，请确保以管理员身份运行。")

	// 使用reg命令作为替代方案
	newGUID := uuid.New().String()
	cmd := exec.Command("reg", "add", `HKLM\SOFTWARE\Microsoft\Cryptography`, "/v", "MachineGuid", "/t", "REG_SZ", "/d", newGUID, "/f")

	if err := cmd.Run(); err != nil {
		r.statusCallback(fmt.Sprintf("❌ 错误: 更新Windows MachineGuid失败: %v", err))
		r.statusCallback("⚠️ 警告: 这通常需要管理员权限。")
	} else {
		r.statusCallback("✅ 成功: Windows MachineGuid更新成功。")
	}
}

// updateMacOSPlatformUUID 处理macOS的平台UUID
func (r *MachineIDResetter) updateMacOSPlatformUUID() {
	r.statusCallback("ℹ️ 信息: 正在检查macOS平台UUID...")

	cmd := exec.Command("ioreg", "-d2", "-c", "IOPlatformExpertDevice")
	output, err := cmd.Output()
	if err != nil {
		r.statusCallback("⚠️ 警告: 无法检查macOS平台UUID。可能是ioreg命令不可用。")
		return
	}

	outputStr := string(output)
	if strings.Contains(outputStr, "IOPlatformUUID") {
		r.statusCallback("ℹ️ 信息: 找到macOS平台UUID信息")
	}

	r.statusCallback("⚠️ 警告: 注意：直接更改macOS平台UUID很复杂且有风险。脚本不会执行此操作。")
}

// UpdateMachineIDFile 更新machineId文件
func (r *MachineIDResetter) UpdateMachineIDFile(machineID string) error {
	if r.paths.MachineIDPath == "" {
		return nil
	}

	r.statusCallback(fmt.Sprintf("ℹ️ 信息: 正在更新machineId文件: %s", r.paths.MachineIDPath))

	// 确保目录存在
	dir := filepath.Dir(r.paths.MachineIDPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(r.paths.MachineIDPath, []byte(machineID), 0644); err != nil {
		return fmt.Errorf("更新machineId文件失败: %v", err)
	}

	r.statusCallback("✅ 成功: machineId文件更新成功。")
	return nil
}

// Run 执行重置流程
func (r *MachineIDResetter) Run() error {
	if r.paths.DBPath == "" {
		return fmt.Errorf("数据库路径为空")
	}

	r.statusCallback("开始重置Cursor机器ID")

	newIDs := r.GenerateNewIDs()

	if err := r.UpdateSQLiteDB(newIDs); err != nil {
		return fmt.Errorf("更新SQLite数据库失败: %v", err)
	}

	r.UpdateSystemSpecificIDs()

	if err := r.UpdateMachineIDFile(newIDs["dev_device_id"]); err != nil {
		return fmt.Errorf("更新machineId文件失败: %v", err)
	}

	r.statusCallback("\n✅ 成功: 机器ID重置完成！")
	r.statusCallback("⚠️ 警告: 请重启Cursor使更改完全生效。")

	return nil
}

// LaunchCursor 启动Cursor应用程序
func LaunchCursor(statusCallback StatusCallback) bool {
	statusCallback("正在尝试启动Cursor")
	paths := GetCursorPaths()

	if paths == nil || paths.AppPath == "" || !utils.FileExists(paths.AppPath) {
		statusCallback(fmt.Sprintf("❌ 错误: 未找到Cursor应用程序路径，无法启动。"))
		if paths != nil {
			statusCallback(fmt.Sprintf("预期路径: %s", paths.AppPath))
		}
		return false
	}

	var cmd *exec.Cmd
	if runtime.GOOS == "darwin" {
		// macOS
		cmd = exec.Command("open", paths.AppPath)
	} else {
		// Windows 和 Linux
		cmd = exec.Command(paths.AppPath)
	}

	if err := cmd.Start(); err != nil {
		statusCallback(fmt.Sprintf("❌ 错误: 启动Cursor时发生错误: %v", err))
		return false
	}

	statusCallback(fmt.Sprintf("✅ 成功: 已发送启动指令到: %s", paths.AppPath))
	return true
}

// TerminateCursorProcesses 终止所有运行的Cursor进程
func TerminateCursorProcesses(statusCallback StatusCallback) (int, bool) {
	system := runtime.GOOS
	statusCallback("正在尝试关闭所有Cursor进程")

	processesKilledTotal := 0
	allFound := true

	var processNames []string
	if system == "windows" {
		processNames = []string{"Cursor.exe", "Code.exe"}
	} else {
		// Darwin (macOS) & Linux
		processNames = []string{"cursor", "Code - OSS"}
	}

	for _, name := range processNames {
		var cmd *exec.Cmd

		if system == "windows" {
			// Windows: 使用taskkill
			cmd = exec.Command("taskkill", "/F", "/IM", name)
		} else {
			// macOS 和 Linux: 使用pkill
			cmd = exec.Command("pkill", "-f", name)
		}

		output, err := cmd.CombinedOutput()

		if err != nil {
			if cmd.ProcessState != nil {
				exitCode := cmd.ProcessState.ExitCode()

				if system == "windows" && exitCode == 128 {
					// 返回码128表示进程未找到，这是预期的如果它没有运行
					continue
				} else if system != "windows" && exitCode == 1 {
					// 返回码1表示没有匹配的进程
					continue
				}
			}

			// 检查是否是命令未找到的错误
			if strings.Contains(err.Error(), "executable file not found") {
				if system == "windows" {
					statusCallback("❌ 错误: 命令 'taskkill' 未找到。")
				} else {
					statusCallback("❌ 错误: 命令 'pkill' 未找到。")
				}
				allFound = false
				break
			}

			statusCallback(fmt.Sprintf("⚠️ 警告: 终止 '%s' 时: %s", name, string(output)))
		} else {
			statusCallback(fmt.Sprintf("✅ 成功: 成功终止进程: %s", name))
			processesKilledTotal++
		}
	}

	// 最终摘要消息
	if processesKilledTotal > 0 {
		statusCallback("\n✅ 成功: 操作完成，相关进程已关闭。")
	} else {
		statusCallback("\nℹ️ 信息: 未找到正在运行的Cursor相关进程。")
	}

	return processesKilledTotal, allFound
}

// RunFullResetFlow 执行完整的环境重置流程
func RunFullResetFlow(statusCallback StatusCallback) error {
	_, allFound := TerminateCursorProcesses(statusCallback)
	if !allFound {
		statusCallback("❌ 错误: 无法确认Cursor进程已关闭，中止操作。")
		return fmt.Errorf("无法确认进程已关闭")
	}

	statusCallback("\n准备重置机器ID")
	time.Sleep(1 * time.Second)

	// 检查管理员权限（仅Windows）
	if runtime.GOOS == "windows" {
		if !isAdmin() {
			statusCallback("❌ 错误: 权限不足！重置ID需要管理员权限。")
			return fmt.Errorf("需要管理员权限")
		}
	}

	resetter := NewMachineIDResetter(statusCallback)
	if resetter == nil {
		return fmt.Errorf("无法创建机器ID重置器")
	}

	if err := resetter.Run(); err != nil {
		return fmt.Errorf("机器ID重置失败: %v", err)
	}

	statusCallback("\n✅ 成功: ID重置完成，准备重启应用...")
	time.Sleep(1 * time.Second)

	LaunchCursor(statusCallback)

	statusCallback("\n\n✅ 成功: 环境重置流程执行完毕！")
	return nil
}

// isAdmin 检查是否具有管理员权限（Windows）
func isAdmin() bool {
	if runtime.GOOS != "windows" {
		return true // 非Windows系统不需要检查
	}

	// 尝试创建一个需要管理员权限的注册表项来测试
	cmd := exec.Command("net", "session")
	err := cmd.Run()
	return err == nil
}
