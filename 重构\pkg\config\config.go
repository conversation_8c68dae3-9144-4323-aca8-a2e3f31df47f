package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"gopkg.in/ini.v1"
)

// Config 配置管理结构体
type Config struct {
	file   *ini.File
	path   string
	mutex  sync.RWMutex
}

// 全局配置缓存
var (
	globalConfig *Config
	configOnce   sync.Once
)

// ConfigPaths 系统路径配置
type ConfigPaths struct {
	StoragePath     string
	SQLitePath      string
	MachineIDPath   string
	CursorPath      string
	UpdaterPath     string
	UpdateYMLPath   string
	ProductJSONPath string
}

// GetConfig 获取全局配置实例
func GetConfig() (*Config, error) {
	var err error
	configOnce.Do(func() {
		globalConfig, err = setupConfig()
	})
	return globalConfig, err
}

// setupConfig 设置配置文件并返回配置对象
func setupConfig() (*Config, error) {
	// 获取文档路径
	docsPath, err := getUserDocumentsPath()
	if err != nil || docsPath == "" {
		// 如果找不到文档路径，使用当前目录
		fmt.Printf("⚠️ 未找到文档路径，使用当前目录\n")
		docsPath, _ = os.Getwd()
	}

	// 标准化路径
	configDir := filepath.Join(docsPath, ".cursor-free-vip")
	configFile := filepath.Join(configDir, "config.ini")

	// 创建配置目录
	if _, err := os.Stat(configDir); os.IsNotExist(err) {
		if err := os.MkdirAll(configDir, 0755); err != nil {
			// 如果无法创建目录，使用临时目录
			tempDir := filepath.Join(os.TempDir(), ".cursor-free-vip")
			configDir = tempDir
			configFile = filepath.Join(configDir, "config.ini")
			os.MkdirAll(configDir, 0755)
			fmt.Printf("⚠️ 因错误使用临时目录: %s (错误: %v)\n", configDir, err)
		} else {
			fmt.Printf("ℹ️ 配置目录已创建: %s\n", configDir)
		}
	}

	// 创建配置对象
	cfg := &Config{
		path: configFile,
	}

	// 加载或创建配置文件
	if err := cfg.loadOrCreateConfig(); err != nil {
		return nil, fmt.Errorf("设置配置时出错: %v", err)
	}

	return cfg, nil
}

// loadOrCreateConfig 加载或创建配置文件
func (c *Config) loadOrCreateConfig() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 获取默认配置
	defaultConfig := c.getDefaultConfig()

	// 检查配置文件是否存在
	if _, err := os.Stat(c.path); os.IsNotExist(err) {
		// 创建新配置文件
		c.file = ini.Empty()
		c.applyDefaultConfig(defaultConfig)
		
		if err := c.file.SaveTo(c.path); err != nil {
			return fmt.Errorf("创建配置文件失败: %v", err)
		}
		fmt.Printf("✅ 配置已创建: %s\n", c.path)
	} else {
		// 加载现有配置文件
		file, err := ini.Load(c.path)
		if err != nil {
			return fmt.Errorf("加载配置文件失败: %v", err)
		}
		c.file = file

		// 合并默认配置
		configModified := c.mergeDefaultConfig(defaultConfig)
		if configModified {
			if err := c.file.SaveTo(c.path); err != nil {
				return fmt.Errorf("保存配置文件失败: %v", err)
			}
			fmt.Printf("✅ 配置已更新\n")
		}
	}

	return nil
}

// getDefaultConfig 获取默认配置
func (c *Config) getDefaultConfig() map[string]map[string]string {
	config := map[string]map[string]string{
		"Browser": {
			"default_browser":     "chrome",
			"chrome_path":         getDefaultBrowserPath(),
			"chrome_driver_path":  getDefaultDriverPath(),
		},
		"Turnstile": {
			"handle_turnstile_time":        "2",
			"handle_turnstile_random_time": "1-3",
		},
		"Timing": {
			"min_random_time":             "0.1",
			"max_random_time":             "0.8",
			"page_load_wait":              "0.1-0.8",
			"input_wait":                  "0.3-0.8",
			"submit_wait":                 "0.5-1.5",
			"verification_code_input":     "0.1-0.3",
			"verification_success_wait":   "2-3",
			"verification_retry_wait":     "2-3",
			"email_check_initial_wait":    "4-6",
			"email_refresh_wait":          "2-4",
			"settings_page_load_wait":     "1-2",
			"failed_retry_time":           "0.5-1",
			"retry_interval":              "8-12",
			"max_timeout":                 "160",
		},
		"Utils": {
			"enabled_update_check":  "true",
			"enabled_force_update":  "false",
			"enabled_account_info":  "true",
		},
		"OAuth": {
			"show_selection_alert": "false",
			"timeout":              "120",
			"max_attempts":         "3",
		},
		"Token": {
			"refresh_server":  "https://token.cursorpro.com.cn",
			"enable_refresh":  "true",
		},
		"Email": {
			"prefix":   "",
			"password": "",
		},
		"TempMailPlus": {
			"enabled": "false",
			"email":   "",
			"epin":    "",
		},
	}

	// 添加系统特定路径配置
	systemPaths := c.getSystemPaths()
	if systemPaths != nil {
		pathSection := make(map[string]string)
		pathSection["storage_path"] = systemPaths.StoragePath
		pathSection["sqlite_path"] = systemPaths.SQLitePath
		pathSection["machine_id_path"] = systemPaths.MachineIDPath
		pathSection["cursor_path"] = systemPaths.CursorPath
		pathSection["updater_path"] = systemPaths.UpdaterPath
		pathSection["update_yml_path"] = systemPaths.UpdateYMLPath
		pathSection["product_json_path"] = systemPaths.ProductJSONPath

		switch runtime.GOOS {
		case "windows":
			config["WindowsPaths"] = pathSection
		case "darwin":
			config["MacPaths"] = pathSection
		case "linux":
			config["LinuxPaths"] = pathSection
		}
	}

	return config
}

// applyDefaultConfig 应用默认配置
func (c *Config) applyDefaultConfig(defaultConfig map[string]map[string]string) {
	for sectionName, options := range defaultConfig {
		section, _ := c.file.NewSection(sectionName)
		for key, value := range options {
			section.NewKey(key, value)
		}
	}
}

// mergeDefaultConfig 合并默认配置
func (c *Config) mergeDefaultConfig(defaultConfig map[string]map[string]string) bool {
	configModified := false

	for sectionName, options := range defaultConfig {
		section := c.file.Section(sectionName)
		if section == nil {
			section, _ = c.file.NewSection(sectionName)
			configModified = true
		}

		for key, value := range options {
			if !section.HasKey(key) {
				section.NewKey(key, value)
				configModified = true
				fmt.Printf("ℹ️ 已添加配置选项: %s.%s\n", sectionName, key)
			}
		}
	}

	return configModified
}

// Get 获取配置值
func (c *Config) Get(section, key string) string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return c.file.Section(section).Key(key).String()
}

// GetWithFallback 获取配置值，如果不存在则返回默认值
func (c *Config) GetWithFallback(section, key, fallback string) string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	value := c.file.Section(section).Key(key).String()
	if value == "" {
		return fallback
	}
	return value
}

// Set 设置配置值
func (c *Config) Set(section, key, value string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	sec := c.file.Section(section)
	if sec == nil {
		sec, _ = c.file.NewSection(section)
	}
	sec.Key(key).SetValue(value)
}

// Save 保存配置到文件
func (c *Config) Save() error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if err := c.file.SaveTo(c.path); err != nil {
		return fmt.Errorf("保存配置时出错: %v", err)
	}
	return nil
}

// GetBool 获取布尔类型配置值
func (c *Config) GetBool(section, key string) bool {
	value := strings.ToLower(c.Get(section, key))
	return value == "true" || value == "yes" || value == "on" || value == "1"
}

// GetInt 获取整数类型配置值
func (c *Config) GetInt(section, key string) int {
	value := c.Get(section, key)
	if value == "" {
		return 0
	}

	var result int
	fmt.Sscanf(value, "%d", &result)
	return result
}

// PrintConfig 打印配置信息
func (c *Config) PrintConfig() {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	fmt.Printf("\nℹ️ 配置:\n")
	fmt.Printf("\n%s\n", strings.Repeat("─", 70))

	for _, section := range c.file.Sections() {
		if section.Name() == "DEFAULT" {
			continue
		}

		fmt.Printf("[%s]\n", section.Name())
		for _, key := range section.Keys() {
			value := key.String()

			// 对布尔值进行特殊处理
			lowerValue := strings.ToLower(value)
			var valueDisplay string
			if lowerValue == "true" || lowerValue == "yes" || lowerValue == "on" || lowerValue == "1" {
				valueDisplay = "已启用"
			} else if lowerValue == "false" || lowerValue == "no" || lowerValue == "off" || lowerValue == "0" {
				valueDisplay = "已禁用"
			} else {
				valueDisplay = value
			}

			fmt.Printf("  %s = %s\n", key.Name(), valueDisplay)
		}
	}

	fmt.Printf("\n%s\n", strings.Repeat("─", 70))
	fmt.Printf("ℹ️ 配置文件: %s\n\n", c.path)
}

// ForceUpdateConfig 强制更新配置文件
func (c *Config) ForceUpdateConfig() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查是否启用强制更新
	if !c.GetBool("Utils", "enabled_force_update") {
		fmt.Printf("\nℹ️ 配置文件强制更新已被配置禁用。保留现有配置文件。\n")
		return nil
	}

	// 创建备份
	currentTime := time.Now()
	backupFile := fmt.Sprintf("%s.bak.%s", c.path, currentTime.Format("20060102_150405"))

	if _, err := os.Stat(c.path); err == nil {
		if err := copyFile(c.path, backupFile); err != nil {
			return fmt.Errorf("备份配置失败: %v", err)
		}
		fmt.Printf("\nℹ️ 已创建备份: %s\n", backupFile)
		fmt.Printf("\nℹ️ 配置文件强制更新已启用\n")

		// 删除原配置文件
		if err := os.Remove(c.path); err != nil {
			return fmt.Errorf("删除配置文件失败: %v", err)
		}
		fmt.Printf("ℹ️ 配置文件已删除以进行强制更新\n")
	}

	// 重新创建配置
	return c.loadOrCreateConfig()
}
