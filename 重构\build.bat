@echo off
REM Cursor管理工具 - Go版本构建脚本 (Windows)

echo 正在构建Cursor管理工具...

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go编译器，请先安装Go语言环境
    pause
    exit /b 1
)

REM 创建构建目录
if not exist build mkdir build

REM 安装依赖
echo 正在安装依赖...
go mod download
go mod tidy

REM 构建程序（Windows GUI应用）
echo 正在构建程序...
go build -ldflags="-H windowsgui" -o build\cursor-manager.exe .\cmd\cursor-manager

if %errorlevel% equ 0 (
    echo 构建成功！可执行文件位于: build\cursor-manager.exe
) else (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 构建完成！
pause
