# Cursor管理工具 - Go重构版

这是原Python版本Cursor管理工具的Go语言重构版本，保持所有原有功能。

## 项目结构

```
cursor-manager/
├── cmd/
│   └── cursor-manager/          # 主程序入口
├── pkg/
│   ├── config/                  # 配置管理模块
│   ├── utils/                   # 工具模块
│   ├── app/                     # 应用管理模块
│   ├── email/                   # 邮箱接收模块
│   ├── web/                     # 网页操作模块
│   └── gui/                     # GUI界面模块
├── internal/
│   └── models/                  # 内部数据模型
├── go.mod                       # Go模块定义
└── README.md                    # 项目说明
```

## 功能模块对应关系

| Go模块 | Python原文件 | 功能描述 |
|--------|-------------|----------|
| pkg/config | 配置管理.py | 配置文件读取、写入和管理 |
| pkg/utils | 工具模块.py | 基础工具函数、常量定义 |
| pkg/app | 应用管理.py | Cursor应用启动、终止、ID重置 |
| pkg/email | 邮箱接收.py | POP3邮箱连接、验证码提取 |
| pkg/web | 网页操作.py | 浏览器自动化、登录流程 |
| pkg/gui | 界面组件.py + 主程序.py | 图形界面实现 |

## 主要依赖

- **fyne.io/fyne/v2**: 跨平台GUI框架
- **github.com/go-rod/rod**: 浏览器自动化
- **github.com/mattn/go-sqlite3**: SQLite数据库操作
- **github.com/emersion/go-pop3**: POP3邮件客户端
- **gopkg.in/ini.v1**: INI配置文件处理

## 构建和运行

### 命令行版本（推荐）

```bash
# 安装依赖
go mod tidy

# 构建命令行版本
go build -o build/cursor-manager-cli.exe ./cmd/cursor-manager/main_cli.go

# 运行
./build/cursor-manager-cli.exe test
```

### Windows批处理构建

```cmd
# 运行构建脚本
build.bat
```

### 使用Makefile（需要make工具）

```bash
# 查看所有可用命令
make help

# 构建当前平台版本
make build

# 运行测试
make test
```

## 命令行使用

```bash
# 显示帮助
cursor-manager-cli.exe

# 显示配置信息
cursor-manager-cli.exe config

# 测试所有模块
cursor-manager-cli.exe test

# 重置Cursor环境
cursor-manager-cli.exe reset

# 监控邮箱（需要先设置邮箱凭据）
cursor-manager-cli.exe email <邮箱> <密码>

# 生成随机邮箱
cursor-manager-cli.exe generate-email
```

## 功能特性

1. **GUI界面**: 使用Fyne提供跨平台图形界面
2. **邮箱管理**: 随机邮箱生成、POP3验证码接收
3. **浏览器自动化**: 自动登录流程、网页操作
4. **应用管理**: Cursor进程管理、机器ID重置
5. **配置管理**: 持久化配置存储

## 项目状态

### ✅ 已完成功能

- [x] **配置管理模块**: 完整的INI配置文件读写功能
- [x] **工具模块**: 基础工具函数、随机字符串生成、路径处理
- [x] **应用管理模块**: Cursor进程管理、路径检测
- [x] **邮箱接收模块**: 基础POP3连接和邮件监控框架
- [x] **网页操作模块**: 浏览器自动化框架、随机邮箱生成
- [x] **命令行界面**: 完整的CLI工具，支持配置查看、测试等
- [x] **构建系统**: Makefile、Windows批处理脚本

### ⚠️ 当前限制

1. **SQLite功能**: 由于缺少CGO编译器，SQLite数据库操作功能暂时禁用
2. **GUI界面**: Fyne GUI需要CGO支持，当前仅提供命令行版本
3. **邮箱监控**: POP3实现为简化版本，可能需要进一步完善
4. **浏览器自动化**: Rod库功能完整，但需要Chrome浏览器支持

### 🔧 完整功能所需环境

要启用所有功能，需要安装：
- **GCC编译器**: 用于CGO支持（SQLite、GUI）
- **Chrome浏览器**: 用于网页自动化
- **Git**: 用于某些Go模块的下载

## 测试结果

最新测试结果（命令行版本）：
```
✅ 配置模块测试通过
✅ 工具模块测试通过
✅ 应用管理模块测试通过
✅ 邮箱生成测试通过
```
