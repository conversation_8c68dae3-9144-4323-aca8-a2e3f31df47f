# Cursor管理工具 - Go重构版

这是原Python版本Cursor管理工具的Go语言重构版本，保持所有原有功能。

## 项目结构

```
cursor-manager/
├── cmd/
│   └── cursor-manager/          # 主程序入口
├── pkg/
│   ├── config/                  # 配置管理模块
│   ├── utils/                   # 工具模块
│   ├── app/                     # 应用管理模块
│   ├── email/                   # 邮箱接收模块
│   ├── web/                     # 网页操作模块
│   └── gui/                     # GUI界面模块
├── internal/
│   └── models/                  # 内部数据模型
├── go.mod                       # Go模块定义
└── README.md                    # 项目说明
```

## 功能模块对应关系

| Go模块 | Python原文件 | 功能描述 |
|--------|-------------|----------|
| pkg/config | 配置管理.py | 配置文件读取、写入和管理 |
| pkg/utils | 工具模块.py | 基础工具函数、常量定义 |
| pkg/app | 应用管理.py | Cursor应用启动、终止、ID重置 |
| pkg/email | 邮箱接收.py | POP3邮箱连接、验证码提取 |
| pkg/web | 网页操作.py | 浏览器自动化、登录流程 |
| pkg/gui | 界面组件.py + 主程序.py | 图形界面实现 |

## 主要依赖

- **fyne.io/fyne/v2**: 跨平台GUI框架
- **github.com/go-rod/rod**: 浏览器自动化
- **github.com/mattn/go-sqlite3**: SQLite数据库操作
- **github.com/emersion/go-pop3**: POP3邮件客户端
- **gopkg.in/ini.v1**: INI配置文件处理

## 构建和运行

### 前置要求

- **Go 1.21+**: Go语言开发环境
- **CGO编译器**: 用于SQLite和GUI支持
  - Windows: MinGW-w64 或 TDM-GCC
  - macOS: Xcode Command Line Tools
  - Linux: GCC

> 📋 **CGO编译器安装指南**: 详细的安装步骤请参考 [CGO_SETUP.md](CGO_SETUP.md)

### Windows批处理构建（推荐）

```cmd
# 运行构建脚本
build.bat
```

### 使用Makefile

```bash
# 查看所有可用命令
make help

# 构建当前平台版本
make build

# 运行程序
make run
```

### 手动构建

```bash
# 安装依赖
go mod tidy

# 构建GUI版本（需要CGO支持）
CGO_ENABLED=1 go build -o build/cursor-manager.exe ./cmd/cursor-manager

# 运行
./build/cursor-manager.exe
```

## 功能特性

1. **GUI界面**: 使用Fyne提供跨平台图形界面
2. **邮箱管理**: 随机邮箱生成、POP3验证码接收
3. **浏览器自动化**: 自动登录流程、网页操作
4. **应用管理**: Cursor进程管理、机器ID重置
5. **配置管理**: 持久化配置存储

## 项目状态

### ✅ 已完成功能

- [x] **配置管理模块**: 完整的INI配置文件读写功能
- [x] **工具模块**: 基础工具函数、随机字符串生成、路径处理
- [x] **应用管理模块**: Cursor进程管理、机器ID重置、SQLite数据库操作
- [x] **邮箱接收模块**: POP3连接和邮件监控功能
- [x] **网页操作模块**: 浏览器自动化、随机邮箱生成、自动登录流程
- [x] **GUI界面**: 完整的图形界面，包含所有原Python版本功能
- [x] **构建系统**: Makefile、Windows批处理脚本，支持CGO编译

### 🎯 功能特性

1. **完整GUI界面**: 使用Fyne提供跨平台图形界面
2. **机器ID重置**: 完整的SQLite数据库操作和系统ID重置
3. **邮箱自动化**: POP3邮件监控和验证码自动提取
4. **浏览器自动化**: 基于Rod的网页操作和自动登录
5. **配置管理**: 持久化配置存储和跨平台路径处理

### 🔧 系统要求

- **Go 1.21+**: Go语言开发环境
- **CGO编译器**: 用于SQLite和GUI支持
- **Chrome浏览器**: 用于网页自动化功能
