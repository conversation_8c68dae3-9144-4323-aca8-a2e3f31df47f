package gui

import (
	"fmt"
	"runtime"
	"strings"
	"syscall"
	"unsafe"

	"cursor-manager/pkg/app"
	"cursor-manager/pkg/config"
	"cursor-manager/pkg/email"
	"cursor-manager/pkg/web"
)

// Windows API 常量
const (
	MB_OK                = 0x00000000
	MB_OKCANCEL          = 0x00000001
	MB_YESNO             = 0x00000004
	MB_ICONINFORMATION   = 0x00000040
	MB_ICONWARNING       = 0x00000030
	MB_ICONERROR         = 0x00000010
	MB_ICONQUESTION      = 0x00000020
	IDOK                 = 1
	IDCANCEL             = 2
	IDYES                = 6
	IDNO                 = 7
)

var (
	user32               = syscall.NewLazyDLL("user32.dll")
	kernel32             = syscall.NewLazyDLL("kernel32.dll")
	procMessageBoxW      = user32.NewProc("MessageBoxW")
	procGetConsoleWindow = kernel32.NewProc("GetConsoleWindow")
	procShowWindow       = user32.NewProc("ShowWindow")
)

// MainWindow 主窗口结构体
type MainWindow struct {
	config *config.Config
}

// NewMainWindow 创建新的主窗口
func NewMainWindow() *MainWindow {
	// 隐藏控制台窗口
	hideConsole()

	cfg, err := config.GetConfig()
	if err != nil {
		showError("配置错误", fmt.Sprintf("无法加载配置文件: %v", err))
		return nil
	}

	return &MainWindow{
		config: cfg,
	}
}

// hideConsole 隐藏控制台窗口
func hideConsole() {
	if runtime.GOOS == "windows" {
		console, _, _ := procGetConsoleWindow.Call()
		if console != 0 {
			// SW_HIDE = 0
			procShowWindow.Call(console, 0)
		}
	}
}

// showMessage 显示信息消息框
func showMessage(title, message string) {
	if runtime.GOOS == "windows" {
		titlePtr, _ := syscall.UTF16PtrFromString(title)
		messagePtr, _ := syscall.UTF16PtrFromString(message)
		procMessageBoxW.Call(0, uintptr(unsafe.Pointer(messagePtr)), uintptr(unsafe.Pointer(titlePtr)), MB_OK|MB_ICONINFORMATION)
	} else {
		fmt.Printf("%s: %s\n", title, message)
	}
}

// showError 显示错误消息框
func showError(title, message string) {
	if runtime.GOOS == "windows" {
		titlePtr, _ := syscall.UTF16PtrFromString(title)
		messagePtr, _ := syscall.UTF16PtrFromString(message)
		procMessageBoxW.Call(0, uintptr(unsafe.Pointer(messagePtr)), uintptr(unsafe.Pointer(titlePtr)), MB_OK|MB_ICONERROR)
	} else {
		fmt.Printf("错误 - %s: %s\n", title, message)
	}
}

// showQuestion 显示确认对话框
func showQuestion(title, message string) bool {
	if runtime.GOOS == "windows" {
		titlePtr, _ := syscall.UTF16PtrFromString(title)
		messagePtr, _ := syscall.UTF16PtrFromString(message)
		ret, _, _ := procMessageBoxW.Call(0, uintptr(unsafe.Pointer(messagePtr)), uintptr(unsafe.Pointer(titlePtr)), MB_YESNO|MB_ICONQUESTION)
		return ret == IDYES
	} else {
		fmt.Printf("确认 - %s: %s (假设选择是)\n", title, message)
		return true
	}
}

// showWarning 显示警告消息框
func showWarning(title, message string) {
	if runtime.GOOS == "windows" {
		titlePtr, _ := syscall.UTF16PtrFromString(title)
		messagePtr, _ := syscall.UTF16PtrFromString(message)
		procMessageBoxW.Call(0, uintptr(unsafe.Pointer(messagePtr)), uintptr(unsafe.Pointer(titlePtr)), MB_OK|MB_ICONWARNING)
	} else {
		fmt.Printf("警告 - %s: %s\n", title, message)
	}
}

// showMainMenu 显示主菜单
func (mw *MainWindow) showMainMenu() {
	for {
		menu := `Cursor管理工具 - 主菜单

请选择操作：

1. 一键登录 (当前页面)
2. 一键重置环境
3. 打开邮箱客户端
4. 生成随机邮箱
5. 配置设置
6. 退出程序

请输入选项 (1-6):`

		choice := mw.getInput("主菜单", menu)
		
		switch choice {
		case "1":
			mw.handleAutoLogin()
		case "2":
			mw.handleResetEnvironment()
		case "3":
			mw.handleEmailClient()
		case "4":
			mw.handleGenerateEmail()
		case "5":
			mw.handleSettings()
		case "6":
			return
		default:
			showWarning("无效选择", "请输入 1-6 之间的数字")
		}
	}
}

// getInput 获取用户输入（简化版本，使用消息框）
func (mw *MainWindow) getInput(title, prompt string) string {
	// 在Windows上，我们使用一个简化的选择方式
	// 实际应用中可能需要更复杂的输入对话框
	if runtime.GOOS == "windows" {
		titlePtr, _ := syscall.UTF16PtrFromString(title)
		promptPtr, _ := syscall.UTF16PtrFromString(prompt)
		procMessageBoxW.Call(0, uintptr(unsafe.Pointer(promptPtr)), uintptr(unsafe.Pointer(titlePtr)), MB_OK|MB_ICONINFORMATION)
		
		// 这里返回默认值，实际应用中需要实现输入对话框
		return "1"
	}
	return "1"
}

// handleAutoLogin 处理一键登录
func (mw *MainWindow) handleAutoLogin() {
	prefix := mw.config.GetWithFallback("Email", "prefix", "")
	password := mw.config.GetWithFallback("Email", "password", "")
	
	if prefix == "" || password == "" {
		showWarning("配置不完整", "请先在设置中配置邮箱前缀和密码")
		return
	}

	if !showQuestion("确认操作", "确定要开始一键登录吗？这将启动自动化流程。") {
		return
	}

	showMessage("操作开始", "一键登录已启动，请等待...")

	go func() {
		monitoringEmail := fmt.Sprintf("%<EMAIL>", prefix)
		loginEmail := web.GenerateRandomEmail(nil)

		statusCallback := func(msg string) {
			// 在后台处理，不显示每个状态
		}

		if err := web.RunAutoLoginFlow(monitoringEmail, loginEmail, password, statusCallback); err != nil {
			showError("登录失败", fmt.Sprintf("一键登录过程中发生错误: %v", err))
		} else {
			showMessage("登录成功", "一键登录流程已完成")
		}
	}()
}

// handleResetEnvironment 处理环境重置
func (mw *MainWindow) handleResetEnvironment() {
	if !showQuestion("确认重置", "确定要重置Cursor环境吗？\n\n这将：\n- 关闭所有Cursor进程\n- 重置机器ID\n- 重启Cursor\n\n此操作不可撤销！") {
		return
	}

	showMessage("操作开始", "环境重置已启动，请等待...")

	go func() {
		statusCallback := func(msg string) {
			// 在后台处理，只显示关键信息
			if strings.Contains(msg, "成功") || strings.Contains(msg, "完成") {
				showMessage("进度更新", msg)
			}
		}

		if err := app.RunFullResetFlow(statusCallback); err != nil {
			showError("重置失败", fmt.Sprintf("环境重置过程中发生错误: %v", err))
		} else {
			showMessage("重置成功", "环境重置已完成！Cursor应该已经重新启动。")
		}
	}()
}

// handleEmailClient 处理邮箱客户端
func (mw *MainWindow) handleEmailClient() {
	prefix := mw.config.GetWithFallback("Email", "prefix", "")
	password := mw.config.GetWithFallback("Email", "password", "")

	if prefix == "" || password == "" {
		showWarning("配置不完整", "请先在设置中配置邮箱前缀和密码")
		return
	}

	emailToWatch := fmt.Sprintf("%<EMAIL>", prefix)
	
	if !showQuestion("确认操作", fmt.Sprintf("确定要启动邮箱客户端监控吗？\n\n监控邮箱: %s", emailToWatch)) {
		return
	}

	showMessage("操作开始", "邮箱客户端已启动，正在后台监控...")

	go func() {
		statusCallback := func(msg string) {
			if strings.Contains(msg, "VERIFICATION_CODE:") {
				code := strings.Split(msg, "VERIFICATION_CODE:")[1]
				showMessage("验证码", fmt.Sprintf("收到验证码: %s", strings.TrimSpace(code)))
			}
		}

		if err := email.LaunchEmailClientProcess(emailToWatch, password, statusCallback); err != nil {
			showError("邮箱客户端错误", fmt.Sprintf("邮箱客户端启动失败: %v", err))
		}
	}()
}

// handleGenerateEmail 处理生成邮箱
func (mw *MainWindow) handleGenerateEmail() {
	email := web.GenerateRandomEmail(nil)
	if email != "" {
		showMessage("随机邮箱", fmt.Sprintf("生成的邮箱地址:\n\n%s\n\n(已复制到剪贴板)", email))
		// 这里可以添加复制到剪贴板的功能
	} else {
		showWarning("生成失败", "无法生成邮箱，请检查配置中的邮箱前缀设置")
	}
}

// handleSettings 处理设置
func (mw *MainWindow) handleSettings() {
	currentPrefix := mw.config.GetWithFallback("Email", "prefix", "")
	currentPassword := mw.config.GetWithFallback("Email", "password", "")
	
	settingsInfo := fmt.Sprintf(`当前配置:

邮箱前缀: %s
邮箱密码: %s

注意: 此版本暂不支持在线修改配置
如需修改，请编辑配置文件或使用Web版本`, 
		func() string { if currentPrefix == "" { return "(未设置)" } else { return currentPrefix } }(),
		func() string { if currentPassword == "" { return "(未设置)" } else { return "******" } }())
	
	showMessage("配置信息", settingsInfo)
}

// Run 运行应用程序
func (mw *MainWindow) Run() {
	showMessage("欢迎", "欢迎使用 Cursor管理工具!\n\n这是一个纯exe版本，无需浏览器和终端。")
	mw.showMainMenu()
}
