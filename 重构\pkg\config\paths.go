package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
)

// getSystemPaths 获取系统特定的路径配置
func (c *Config) getSystemPaths() *ConfigPaths {
	switch runtime.GOOS {
	case "windows":
		return c.getWindowsPaths()
	case "darwin":
		return c.getMacPaths()
	case "linux":
		return c.getLinuxPaths()
	default:
		return nil
	}
}

// getWindowsPaths 获取Windows系统路径
func (c *Config) getWindowsPaths() *ConfigPaths {
	appdata := os.Getenv("APPDATA")
	localappdata := os.Getenv("LOCALAPPDATA")

	if appdata == "" || localappdata == "" {
		fmt.Printf("❌ APPDATA 或 LOCALAPPDATA 环境变量未找到\n")
		// 回退到临时目录避免崩溃
		appdata = os.TempDir()
		localappdata = os.TempDir()
	}

	paths := &ConfigPaths{
		StoragePath:     filepath.Join(appdata, "Cursor", "User", "globalStorage", "storage.json"),
		SQLitePath:      filepath.Join(appdata, "Cursor", "User", "globalStorage", "state.vscdb"),
		MachineIDPath:   filepath.Join(appdata, "Cursor", "machineId"),
		CursorPath:      filepath.Join(localappdata, "Programs", "Cursor", "resources", "app"),
		UpdaterPath:     filepath.Join(localappdata, "cursor-updater"),
		UpdateYMLPath:   filepath.Join(localappdata, "Programs", "Cursor", "resources", "app-update.yml"),
		ProductJSONPath: filepath.Join(localappdata, "Programs", "Cursor", "resources", "app", "product.json"),
	}

	// 创建存储目录
	storageDir := filepath.Dir(paths.StoragePath)
	os.MkdirAll(storageDir, 0755)

	return paths
}

// getMacPaths 获取macOS系统路径
func (c *Config) getMacPaths() *ConfigPaths {
	homeDir, _ := os.UserHomeDir()

	paths := &ConfigPaths{
		StoragePath:     filepath.Join(homeDir, "Library", "Application Support", "Cursor", "User", "globalStorage", "storage.json"),
		SQLitePath:      filepath.Join(homeDir, "Library", "Application Support", "Cursor", "User", "globalStorage", "state.vscdb"),
		MachineIDPath:   filepath.Join(homeDir, "Library", "Application Support", "Cursor", "machineId"),
		CursorPath:      "/Applications/Cursor.app/Contents/Resources/app",
		UpdaterPath:     filepath.Join(homeDir, "Library", "Application Support", "cursor-updater"),
		UpdateYMLPath:   "/Applications/Cursor.app/Contents/Resources/app-update.yml",
		ProductJSONPath: "/Applications/Cursor.app/Contents/Resources/app/product.json",
	}

	// 创建存储目录
	storageDir := filepath.Dir(paths.StoragePath)
	os.MkdirAll(storageDir, 0755)

	return paths
}

// getLinuxPaths 获取Linux系统路径
func (c *Config) getLinuxPaths() *ConfigPaths {
	// 获取实际用户的主目录，处理sudo和普通情况
	sudoUser := os.Getenv("SUDO_USER")
	currentUser := os.Getenv("USER")
	if currentUser == "" {
		currentUser = os.Getenv("USERNAME")
	}

	if sudoUser != "" {
		currentUser = sudoUser
	}

	if currentUser == "" {
		homeDir, _ := os.UserHomeDir()
		currentUser = filepath.Base(homeDir)
	}

	// 处理sudo情况
	var actualHome, rootHome string
	if sudoUser != "" {
		actualHome = filepath.Join("/home", sudoUser)
		rootHome = "/root"
	} else {
		actualHome = filepath.Join("/home", currentUser)
	}

	if _, err := os.Stat(actualHome); os.IsNotExist(err) {
		actualHome, _ = os.UserHomeDir()
	}

	// 定义基础配置目录
	configBase := filepath.Join(actualHome, ".config")

	// 尝试查找Cursor目录
	var cursorDir string
	possiblePaths := []string{
		filepath.Join(configBase, "Cursor"),
		filepath.Join(configBase, "cursor"),
	}

	if rootHome != "" {
		possiblePaths = append(possiblePaths,
			filepath.Join(rootHome, ".config", "Cursor"),
			filepath.Join(rootHome, ".config", "cursor"),
		)
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			cursorDir = path
			break
		}
	}

	if cursorDir == "" {
		fmt.Printf("⚠️ 在 %s 中未找到 Cursor 或 cursor 目录\n", configBase)
		if rootHome != "" {
			fmt.Printf("ℹ️ 同时检查了 %s/.config\n", rootHome)
		}
		fmt.Printf("ℹ️ 请确保 Cursor 已安装并至少运行过一次\n")
	}

	// 定义路径
	var storagePath, sqlitePath, machineIDPath, updateYMLPath, productJSONPath string
	if cursorDir != "" {
		storagePath = filepath.Join(cursorDir, "User", "globalStorage", "storage.json")
		sqlitePath = filepath.Join(cursorDir, "User", "globalStorage", "state.vscdb")
		machineIDPath = filepath.Join(cursorDir, "machineid")
		updateYMLPath = filepath.Join(cursorDir, "resources", "app-update.yml")
		productJSONPath = filepath.Join(cursorDir, "resources", "app", "product.json")

		// 验证存储文件
		c.verifyLinuxStorageFile(storagePath)
	}

	paths := &ConfigPaths{
		StoragePath:     storagePath,
		SQLitePath:      sqlitePath,
		MachineIDPath:   machineIDPath,
		CursorPath:      getLinuxCursorPath(),
		UpdaterPath:     filepath.Join(configBase, "cursor-updater"),
		UpdateYMLPath:   updateYMLPath,
		ProductJSONPath: productJSONPath,
	}

	return paths
}

// verifyLinuxStorageFile 验证Linux存储文件
func (c *Config) verifyLinuxStorageFile(storagePath string) {
	if storagePath == "" {
		return
	}

	storageDir := filepath.Dir(storagePath)
	if _, err := os.Stat(storageDir); os.IsNotExist(err) {
		fmt.Printf("⚠️ 未找到存储目录: %s\n", storageDir)
		fmt.Printf("ℹ️ 请确保 Cursor 已安装并至少运行过一次\n")
		return
	}

	if stat, err := os.Stat(storagePath); err == nil {
		fmt.Printf("✅ 找到存储文件: %s\n", storagePath)
		fmt.Printf("ℹ️ 文件大小: %d 字节\n", stat.Size())
		fmt.Printf("ℹ️ 文件权限: %o\n", stat.Mode().Perm())

		// 检查文件是否可读写
		if _, err := os.OpenFile(storagePath, os.O_RDWR, 0); err != nil {
			fmt.Printf("❌ 权限被拒绝: %s\n", storagePath)
			fmt.Printf("ℹ️ 请检查文件权限\n")
		} else {
			// 尝试读取文件验证是否损坏
			if content, err := os.ReadFile(storagePath); err != nil {
				fmt.Printf("❌ 读取存储文件时出错: %v\n", err)
				fmt.Printf("ℹ️ 文件可能已损坏。请重新安装 Cursor\n")
			} else if len(content) == 0 {
				fmt.Printf("⚠️ 存储文件为空: %s\n", storagePath)
				fmt.Printf("ℹ️ 文件可能已损坏，请重新安装 Cursor\n")
			} else {
				fmt.Printf("✅ 存储文件有效且包含数据\n")
			}
		}
	} else {
		fmt.Printf("⚠️ 未找到存储文件: %s\n", storagePath)
		fmt.Printf("ℹ️ 请确保 Cursor 已安装并至少运行过一次\n")
	}
}

// getLinuxCursorPath 获取Linux Cursor路径
func getLinuxCursorPath() string {
	// 这是一个通用路径，可能因系统而异
	// 更健壮的解决方案可能需要搜索PATH
	return "/usr/bin/cursor"
}
