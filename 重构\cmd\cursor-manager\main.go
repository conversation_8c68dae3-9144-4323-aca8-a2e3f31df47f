package main

import (
	"log"
	"os"
	"runtime"

	"cursor-manager/pkg/gui"
)

func main() {
	// 设置全局异常处理
	setupGlobalExceptionHandler()

	// 确保在Windows上运行
	if runtime.GOOS != "windows" {
		log.Fatal("此版本仅支持Windows系统")
		return
	}

	// 创建并运行主窗口
	mainWindow := gui.NewMainWindow()
	if mainWindow == nil {
		// 错误已在NewMainWindow中显示
		return
	}

	mainWindow.Run()
}

// setupGlobalExceptionHandler 设置全局异常处理器
func setupGlobalExceptionHandler() {
	// 创建日志文件
	logFile, err := os.OpenFile("error.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("无法创建日志文件: %v", err)
		return
	}

	// 设置日志输出到文件
	log.SetOutput(logFile)
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// 捕获panic
	defer func() {
		if r := recover(); r != nil {
			log.Printf("程序发生严重错误: %v", r)
		}
	}()
}
