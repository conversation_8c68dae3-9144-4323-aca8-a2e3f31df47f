package email

import (
	"bufio"
	"fmt"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// 全局配置
const (
	POP3Server              = "pop.2925.com"
	POP3Port                = 110
	RefreshIntervalSeconds  = 1
)

// EmailClient POP3邮件客户端
type EmailClient struct {
	server   string
	port     int
	account  string
	password string
	callback func(string)
	conn     net.Conn
}

// NewEmailClient 创建新的邮件客户端
func NewEmailClient(account, password string, callback func(string)) *EmailClient {
	return &EmailClient{
		server:   POP3Server,
		port:     POP3Port,
		account:  account,
		password: password,
		callback: callback,
	}
}

// decodePayload 安全地解码邮件内容
func decodePayload(payload []byte, charset string) string {
	// 简化版本，Go的标准库通常能很好地处理编码
	return string(payload)
}

// getCleanBodyFromMsg 解析邮件消息并返回纯净的文本正文
func getCleanBodyFromMsg(bodyContent string) string {
	// 简单的HTML标签移除（实际应用中可能需要更复杂的HTML解析）
	if strings.Contains(bodyContent, "<") {
		re := regexp.MustCompile(`<[^>]*>`)
		bodyContent = re.ReplaceAllString(bodyContent, "")
	}

	return bodyContent
}

// findCodeInText 使用正则表达式在字符串中查找6位验证码
func findCodeInText(bodyText string) string {
	// 匹配格式: 123456, 123 456, 1 2 3 4 5 6
	patterns := []string{
		`\b\d{6}\b`,
		`\b\d{3}\s\d{3}\b`,
		`\b(?:\d\s){5}\d\b`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		match := re.FindString(bodyText)
		if match != "" {
			// 返回去除了空格的验证码
			return strings.ReplaceAll(match, " ", "")
		}
	}

	return ""
}

// connect 连接到POP3服务器
func (c *EmailClient) connect() error {
	address := fmt.Sprintf("%s:%d", c.server, c.port)
	conn, err := net.Dial("tcp", address)
	if err != nil {
		return fmt.Errorf("连接POP3服务器失败: %v", err)
	}
	c.conn = conn
	return nil
}

// sendCommand 发送POP3命令
func (c *EmailClient) sendCommand(command string) (string, error) {
	if c.conn == nil {
		return "", fmt.Errorf("未连接到服务器")
	}

	_, err := c.conn.Write([]byte(command + "\r\n"))
	if err != nil {
		return "", err
	}

	reader := bufio.NewReader(c.conn)
	response, _, err := reader.ReadLine()
	if err != nil {
		return "", err
	}

	return string(response), nil
}

// authenticate 认证用户
func (c *EmailClient) authenticate() error {
	// 读取欢迎消息
	reader := bufio.NewReader(c.conn)
	_, _, err := reader.ReadLine()
	if err != nil {
		return err
	}

	// 发送用户名
	_, err = c.sendCommand(fmt.Sprintf("USER %s", c.account))
	if err != nil {
		return err
	}

	// 发送密码
	_, err = c.sendCommand(fmt.Sprintf("PASS %s", c.password))
	if err != nil {
		return err
	}

	return nil
}

// getMessageCount 获取邮件数量
func (c *EmailClient) getMessageCount() (int, error) {
	response, err := c.sendCommand("STAT")
	if err != nil {
		return 0, err
	}

	// 解析响应 "+OK 5 1024"
	parts := strings.Fields(response)
	if len(parts) < 2 {
		return 0, fmt.Errorf("无效的STAT响应")
	}

	count, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, err
	}

	return count, nil
}

// close 关闭连接
func (c *EmailClient) close() {
	if c.conn != nil {
		c.sendCommand("QUIT")
		c.conn.Close()
		c.conn = nil
	}
}

// MonitorEmails 监控邮件并查找验证码（简化版本）
func (c *EmailClient) MonitorEmails() error {
	c.callback(fmt.Sprintf("正在监控邮箱: %s", c.account))

	// 建立基线
	if err := c.connect(); err != nil {
		return fmt.Errorf("连接POP3服务器失败: %v", err)
	}

	if err := c.authenticate(); err != nil {
		c.close()
		return fmt.Errorf("登录失败，请检查凭据: %v", err)
	}

	c.callback("验证成功。")

	initialCount, err := c.getMessageCount()
	if err != nil {
		c.close()
		return fmt.Errorf("获取邮件数量失败: %v", err)
	}

	c.callback(fmt.Sprintf("基线已建立，当前有 %d 封邮件。开始监控新邮件...", initialCount))
	c.close()

	// 进入监控循环
	loopCounter := 0
	for {
		// 重新连接
		if err := c.connect(); err != nil {
			c.callback(fmt.Sprintf("重连失败: %v，等待10秒后重试...", err))
			time.Sleep(10 * time.Second)
			continue
		}

		if err := c.authenticate(); err != nil {
			c.close()
			c.callback(fmt.Sprintf("重新认证失败: %v，等待10秒后重试...", err))
			time.Sleep(10 * time.Second)
			continue
		}

		// 获取当前邮件数量
		currentCount, err := c.getMessageCount()
		if err != nil {
			c.close()
			c.callback(fmt.Sprintf("获取邮件数量失败: %v，等待10秒后重试...", err))
			time.Sleep(10 * time.Second)
			continue
		}

		// 检查是否有新邮件
		if currentCount > initialCount {
			loopCounter = 0
			newCount := currentCount - initialCount
			c.callback(fmt.Sprintf("\n发现 %d 封新邮件，正在检查...", newCount))

			// 检查新邮件（简化版本：只检查最新的邮件）
			for i := initialCount + 1; i <= currentCount; i++ {
				// 获取邮件内容（这里简化处理，实际需要实现RETR命令）
				response, err := c.sendCommand(fmt.Sprintf("RETR %d", i))
				if err != nil {
					c.callback(fmt.Sprintf("获取邮件 %d 失败: %v", i, err))
					continue
				}

				// 简化的邮件内容处理
				body := getCleanBodyFromMsg(response)
				code := findCodeInText(body)

				if code != "" {
					c.callback(fmt.Sprintf("成功提取到新邮件中的验证码: %s", code))
					c.callback(fmt.Sprintf("VERIFICATION_CODE:%s", code))
					c.close()
					return nil
				}
			}

			c.callback("新邮件中未发现验证码，将继续监控...")
			initialCount = currentCount
		} else {
			loopCounter++
			if loopCounter%15 == 1 {
				c.callback(fmt.Sprintf("没有新邮件，继续监控... (%s)", time.Now().Format("15:04:05")))
			}
		}

		c.close()
		time.Sleep(RefreshIntervalSeconds * time.Second)
	}
}

// LaunchEmailClientProcess 启动邮件客户端进程（简化版本）
func LaunchEmailClientProcess(email, password string, statusCallback func(string)) error {
	client := NewEmailClient(email, password, statusCallback)
	
	go func() {
		if err := client.MonitorEmails(); err != nil {
			statusCallback(fmt.Sprintf("邮件监控出错: %v", err))
		}
	}()
	
	statusCallback(fmt.Sprintf("成功: 邮箱客户端已在后台启动，监控邮箱: %s", email))
	return nil
}
