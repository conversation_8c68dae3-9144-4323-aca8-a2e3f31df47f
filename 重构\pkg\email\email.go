package email

import (
	"fmt"
	"io"
	"net/mail"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/emersion/go-pop3"
)

// 全局配置
const (
	POP3Server              = "pop.2925.com"
	POP3Port                = 110
	RefreshIntervalSeconds  = 1
)

// EmailClient POP3邮件客户端
type EmailClient struct {
	server   string
	port     int
	account  string
	password string
	callback func(string)
}

// NewEmailClient 创建新的邮件客户端
func NewEmailClient(account, password string, callback func(string)) *EmailClient {
	return &EmailClient{
		server:   POP3Server,
		port:     POP3Port,
		account:  account,
		password: password,
		callback: callback,
	}
}

// decodePayload 安全地解码邮件内容
func decodePayload(payload []byte, charset string) string {
	// 简化版本，Go的标准库通常能很好地处理编码
	return string(payload)
}

// getCleanBodyFromMsg 解析邮件消息并返回纯净的文本正文
func getCleanBodyFromMsg(msg *mail.Message) (string, error) {
	// 读取邮件正文
	body, err := io.ReadAll(msg.Body)
	if err != nil {
		return "", err
	}

	bodyStr := string(body)
	
	// 如果是HTML内容，需要提取文本（这里简化处理）
	contentType := msg.Header.Get("Content-Type")
	if strings.Contains(contentType, "text/html") {
		// 简单的HTML标签移除（实际应用中可能需要更复杂的HTML解析）
		re := regexp.MustCompile(`<[^>]*>`)
		bodyStr = re.ReplaceAllString(bodyStr, "")
	}

	return bodyStr, nil
}

// findCodeInText 使用正则表达式在字符串中查找6位验证码
func findCodeInText(bodyText string) string {
	// 匹配格式: 123456, 123 456, 1 2 3 4 5 6
	patterns := []string{
		`\b\d{6}\b`,
		`\b\d{3}\s\d{3}\b`,
		`\b(?:\d\s){5}\d\b`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		match := re.FindString(bodyText)
		if match != "" {
			// 返回去除了空格的验证码
			return strings.ReplaceAll(match, " ", "")
		}
	}

	return ""
}

// establishBaseline 获取当前所有邮件的UIDL，建立基线
func (c *EmailClient) establishBaseline(client *pop3.Client) (map[string]bool, error) {
	// 获取邮件列表
	messages, err := client.ListMessages()
	if err != nil {
		return nil, fmt.Errorf("获取邮件列表失败: %v", err)
	}

	seenUIDs := make(map[string]bool)
	for i := range messages {
		// 使用消息序号作为UID（简化版本）
		uid := strconv.Itoa(i + 1)
		seenUIDs[uid] = true
	}

	c.callback(fmt.Sprintf("基线已建立，当前有 %d 封邮件。开始监控新邮件...", len(seenUIDs)))
	return seenUIDs, nil
}

// MonitorEmails 监控邮件并查找验证码
func (c *EmailClient) MonitorEmails() error {
	c.callback(fmt.Sprintf("正在监控邮箱: %s", c.account))

	// 1. 首次连接并建立基线
	client, err := pop3.Dial(fmt.Sprintf("%s:%d", c.server, c.port))
	if err != nil {
		return fmt.Errorf("连接POP3服务器失败: %v", err)
	}

	if err := client.Auth(c.account, c.password); err != nil {
		client.Close()
		return fmt.Errorf("登录失败，请检查凭据: %v", err)
	}

	c.callback("验证成功。")

	seenUIDs, err := c.establishBaseline(client)
	if err != nil {
		client.Close()
		return fmt.Errorf("建立基线失败: %v", err)
	}

	client.Close()

	// 2. 进入监控循环
	loopCounter := 0
	for {
		// 重新连接
		client, err := pop3.Dial(fmt.Sprintf("%s:%d", c.server, c.port))
		if err != nil {
			c.callback(fmt.Sprintf("重连失败: %v，等待10秒后重试...", err))
			time.Sleep(10 * time.Second)
			continue
		}

		if err := client.Auth(c.account, c.password); err != nil {
			client.Close()
			c.callback(fmt.Sprintf("重新认证失败: %v，等待10秒后重试...", err))
			time.Sleep(10 * time.Second)
			continue
		}

		// 获取当前邮件列表
		messages, err := client.ListMessages()
		if err != nil {
			client.Close()
			c.callback(fmt.Sprintf("获取邮件列表失败: %v，等待10秒后重试...", err))
			time.Sleep(10 * time.Second)
			continue
		}

		// 检查新邮件
		newUIDs := make([]string, 0)
		currentUIDs := make(map[string]bool)

		for i := range messages {
			uid := strconv.Itoa(i + 1)
			currentUIDs[uid] = true
			if !seenUIDs[uid] {
				newUIDs = append(newUIDs, uid)
			}
		}

		if len(newUIDs) > 0 {
			loopCounter = 0 // 重置计数器
			c.callback(fmt.Sprintf("\n发现 %d 封新邮件，正在检查...", len(newUIDs)))

			// 检查新邮件中的验证码
			for _, uid := range newUIDs {
				msgNum, _ := strconv.Atoi(uid)
				
				// 获取邮件内容
				msgReader, err := client.RetrieveMessage(msgNum)
				if err != nil {
					c.callback(fmt.Sprintf("获取邮件 %s 失败: %v", uid, err))
					continue
				}

				// 解析邮件
				msg, err := mail.ReadMessage(msgReader)
				if err != nil {
					c.callback(fmt.Sprintf("解析邮件 %s 失败: %v", uid, err))
					continue
				}

				// 提取邮件正文
				body, err := getCleanBodyFromMsg(msg)
				if err != nil {
					c.callback(fmt.Sprintf("提取邮件 %s 正文失败: %v", uid, err))
					continue
				}

				// 查找验证码
				code := findCodeInText(body)
				if code != "" {
					// 找到验证码
					c.callback(fmt.Sprintf("成功提取到新邮件中的验证码: %s", code))
					c.callback(fmt.Sprintf("VERIFICATION_CODE:%s", code))
					
					client.Close()
					return nil // 任务完成，退出程序
				}
			}

			// 如果检查完所有新邮件都没找到验证码，则更新基线
			c.callback("新邮件中未发现验证码，将继续监控...")
			for uid := range currentUIDs {
				seenUIDs[uid] = true
			}
		} else {
			loopCounter++
			// 每 15 秒打印一次状态，避免刷屏
			if loopCounter%15 == 1 {
				c.callback(fmt.Sprintf("没有新邮件，继续监控... (%s)", time.Now().Format("15:04:05")))
			}
		}

		client.Close()
		time.Sleep(RefreshIntervalSeconds * time.Second)
	}
}

// LaunchEmailClientProcess 启动邮件客户端进程（简化版本）
func LaunchEmailClientProcess(email, password string, statusCallback func(string)) error {
	client := NewEmailClient(email, password, statusCallback)
	
	go func() {
		if err := client.MonitorEmails(); err != nil {
			statusCallback(fmt.Sprintf("邮件监控出错: %v", err))
		}
	}()
	
	statusCallback(fmt.Sprintf("成功: 邮箱客户端已在后台启动，监控邮箱: %s", email))
	return nil
}
