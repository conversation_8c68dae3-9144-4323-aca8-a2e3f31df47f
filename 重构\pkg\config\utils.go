package config

import (
	"io"
	"os"
	"path/filepath"
	"runtime"
)

// getUserDocumentsPath 获取用户文档路径
func getUserDocumentsPath() (string, error) {
	switch runtime.GOOS {
	case "windows":
		// Windows: 使用USERPROFILE环境变量
		userProfile := os.Getenv("USERPROFILE")
		if userProfile != "" {
			return filepath.Join(userProfile, "Documents"), nil
		}
		// 回退方案
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		return filepath.Join(homeDir, "Documents"), nil

	case "darwin":
		// macOS: 使用用户主目录下的Documents
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		return filepath.Join(homeDir, "Documents"), nil

	case "linux":
		// Linux: 使用用户主目录下的Documents
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		return filepath.Join(homeDir, "Documents"), nil

	default:
		// 其他系统：使用用户主目录
		return os.UserHomeDir()
	}
}

// getDefaultBrowserPath 获取默认浏览器路径
func getDefaultBrowserPath() string {
	switch runtime.GOOS {
	case "windows":
		// Windows Chrome路径
		localAppData := os.Getenv("LOCALAPPDATA")
		if localAppData != "" {
			chromePath := filepath.Join(localAppData, "Google", "Chrome", "Application", "chrome.exe")
			if _, err := os.Stat(chromePath); err == nil {
				return chromePath
			}
		}
		
		// 系统级安装路径
		programFiles := os.Getenv("PROGRAMFILES")
		if programFiles != "" {
			chromePath := filepath.Join(programFiles, "Google", "Chrome", "Application", "chrome.exe")
			if _, err := os.Stat(chromePath); err == nil {
				return chromePath
			}
		}
		
		programFilesX86 := os.Getenv("PROGRAMFILES(X86)")
		if programFilesX86 != "" {
			chromePath := filepath.Join(programFilesX86, "Google", "Chrome", "Application", "chrome.exe")
			if _, err := os.Stat(chromePath); err == nil {
				return chromePath
			}
		}
		
		return ""

	case "darwin":
		// macOS Chrome路径
		chromePath := "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
		if _, err := os.Stat(chromePath); err == nil {
			return chromePath
		}
		return ""

	case "linux":
		// Linux Chrome路径
		possiblePaths := []string{
			"/usr/bin/google-chrome",
			"/usr/bin/google-chrome-stable",
			"/usr/bin/chromium-browser",
			"/usr/bin/chromium",
		}
		
		for _, path := range possiblePaths {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
		return ""

	default:
		return ""
	}
}

// getDefaultDriverPath 获取默认驱动路径
func getDefaultDriverPath() string {
	switch runtime.GOOS {
	case "windows":
		// Windows ChromeDriver路径
		return "./drivers/chromedriver.exe"
	case "darwin":
		// macOS ChromeDriver路径
		return "./drivers/chromedriver"
	case "linux":
		// Linux ChromeDriver路径
		return "./drivers/chromedriver"
	default:
		return "./drivers/chromedriver"
	}
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	// 复制文件权限
	sourceInfo, err := os.Stat(src)
	if err != nil {
		return err
	}

	return os.Chmod(dst, sourceInfo.Mode())
}

// fileExists 检查文件是否存在
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// dirExists 检查目录是否存在
func dirExists(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		return false
	}
	return info.IsDir()
}

// ensureDir 确保目录存在，如果不存在则创建
func ensureDir(path string) error {
	if !dirExists(path) {
		return os.MkdirAll(path, 0755)
	}
	return nil
}
