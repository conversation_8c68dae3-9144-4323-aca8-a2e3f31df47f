package gui

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os/exec"
	"runtime"
	"strings"
	"time"

	"cursor-manager/pkg/app"
	"cursor-manager/pkg/config"
	"cursor-manager/pkg/email"
	"cursor-manager/pkg/web"
)

// MainWindow 主窗口结构体
type MainWindow struct {
	config *config.Config
	server *http.Server
}

// APIResponse API响应结构
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// NewMainWindow 创建新的主窗口
func NewMainWindow() *MainWindow {
	cfg, err := config.GetConfig()
	if err != nil {
		fmt.Printf("无法加载或创建配置文件: %v\n", err)
		return nil
	}

	mw := &MainWindow{
		config: cfg,
	}

	// 启动HTTP服务器
	mw.startServer()

	// 自动打开浏览器
	go func() {
		time.Sleep(500 * time.Millisecond) // 等待服务器启动
		mw.openBrowser("http://localhost:8080")
	}()

	return mw
}

// startServer 启动HTTP服务器
func (mw *MainWindow) startServer() {
	mux := http.NewServeMux()

	// 静态文件服务
	mux.HandleFunc("/", mw.handleIndex)

	// API路由
	mux.HandleFunc("/api/config", mw.handleConfig)
	mux.HandleFunc("/api/auto-login", mw.handleAutoLogin)
	mux.HandleFunc("/api/reset-env", mw.handleResetEnv)
	mux.HandleFunc("/api/email-client", mw.handleEmailClient)
	mux.HandleFunc("/api/save-credentials", mw.handleSaveCredentials)
	mux.HandleFunc("/api/generate-email", mw.handleGenerateEmail)

	mw.server = &http.Server{
		Addr:    ":8080",
		Handler: mux,
	}

	go func() {
		if err := mw.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("HTTP服务器启动失败: %v\n", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)
}

// handleIndex 处理主页
func (mw *MainWindow) handleIndex(w http.ResponseWriter, r *http.Request) {
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor管理工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        input, button, textarea { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px; }
        button { background-color: #007cba; color: white; cursor: pointer; }
        button:hover { background-color: #005a87; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .status { height: 300px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; font-family: monospace; font-size: 12px; }
        .form-row { display: flex; align-items: center; margin: 5px 0; }
        .form-row label { width: 100px; display: inline-block; }
        .form-row input { flex: 1; }
        .email-display { background: #e8f4fd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Cursor管理工具</h1>
        
        <div class="section">
            <h3>一键登录工具</h3>
            <button id="autoLoginBtn" onclick="autoLogin()">🔑 一键登录 (当前页面)</button>
        </div>

        <div class="section">
            <h3>邮箱凭据设置</h3>
            <div class="form-row">
                <label>邮箱前缀:</label>
                <input type="text" id="emailPrefix" placeholder="邮箱前缀">
            </div>
            <div class="form-row">
                <label>邮箱密码:</label>
                <input type="password" id="emailPassword" placeholder="邮箱密码">
            </div>
            <button onclick="saveCredentials()">保存凭据</button>
        </div>

        <div class="section">
            <h3>账号信息</h3>
            <div class="email-display">
                <strong>随机邮箱:</strong> <span id="randomEmail">加载中...</span>
                <button onclick="copyEmail()">复制</button>
            </div>
        </div>

        <div class="section">
            <h3>系统工具</h3>
            <button id="resetEnvBtn" onclick="resetEnvironment()">一键重置环境</button>
            <button id="emailClientBtn" onclick="openEmailClient()">打开邮箱客户端</button>
        </div>

        <div class="section">
            <h3>状态信息</h3>
            <div id="status" class="status">准备就绪。请点击按钮开始。</div>
        </div>
    </div>

    <script>
        let isProcessing = false;

        // 加载配置
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const data = await response.json();
                if (data.success && data.data) {
                    document.getElementById('emailPrefix').value = data.data.email_prefix || '';
                    document.getElementById('emailPassword').value = data.data.email_password || '';
                }
            } catch (error) {
                updateStatus('加载配置失败: ' + error.message);
            }
        }

        // 生成随机邮箱
        async function generateEmail() {
            try {
                const response = await fetch('/api/generate-email');
                const data = await response.json();
                if (data.success) {
                    document.getElementById('randomEmail').textContent = data.data.email;
                }
            } catch (error) {
                console.error('生成邮箱失败:', error);
            }
        }

        // 更新状态
        function updateStatus(message) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML += message + '<br>';
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        // 禁用/启用按钮
        function setButtonsEnabled(enabled) {
            isProcessing = !enabled;
            document.getElementById('autoLoginBtn').disabled = !enabled;
            document.getElementById('resetEnvBtn').disabled = !enabled;
            document.getElementById('emailClientBtn').disabled = !enabled;
        }

        // 一键登录
        async function autoLogin() {
            if (isProcessing) return;
            setButtonsEnabled(false);
            updateStatus('开始一键登录...');
            
            try {
                const response = await fetch('/api/auto-login', { method: 'POST' });
                const data = await response.json();
                updateStatus(data.message);
            } catch (error) {
                updateStatus('一键登录失败: ' + error.message);
            } finally {
                setButtonsEnabled(true);
            }
        }

        // 重置环境
        async function resetEnvironment() {
            if (isProcessing) return;
            if (!confirm('确定要重置Cursor环境吗？这将关闭所有Cursor进程并重置机器ID。')) return;
            
            setButtonsEnabled(false);
            updateStatus('开始重置环境...');
            
            try {
                const response = await fetch('/api/reset-env', { method: 'POST' });
                const data = await response.json();
                updateStatus(data.message);
            } catch (error) {
                updateStatus('重置环境失败: ' + error.message);
            } finally {
                setButtonsEnabled(true);
            }
        }

        // 打开邮箱客户端
        async function openEmailClient() {
            if (isProcessing) return;
            setButtonsEnabled(false);
            updateStatus('启动邮箱客户端...');
            
            try {
                const response = await fetch('/api/email-client', { method: 'POST' });
                const data = await response.json();
                updateStatus(data.message);
            } catch (error) {
                updateStatus('启动邮箱客户端失败: ' + error.message);
            } finally {
                setButtonsEnabled(true);
            }
        }

        // 保存凭据
        async function saveCredentials() {
            const prefix = document.getElementById('emailPrefix').value;
            const password = document.getElementById('emailPassword').value;
            
            try {
                const response = await fetch('/api/save-credentials', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prefix, password })
                });
                const data = await response.json();
                updateStatus(data.message);
                if (data.success) {
                    generateEmail(); // 重新生成邮箱
                }
            } catch (error) {
                updateStatus('保存凭据失败: ' + error.message);
            }
        }

        // 复制邮箱
        function copyEmail() {
            const email = document.getElementById('randomEmail').textContent;
            navigator.clipboard.writeText(email).then(() => {
                updateStatus('邮箱已复制到剪贴板: ' + email);
            });
        }

        // 页面加载完成后初始化
        window.onload = function() {
            loadConfig();
            generateEmail();
            setInterval(generateEmail, 5000); // 每5秒更新一次邮箱
        };
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// handleConfig 处理配置请求
func (mw *MainWindow) handleConfig(w http.ResponseWriter, r *http.Request) {
	emailPrefix := mw.config.GetWithFallback("Email", "prefix", "")
	emailPassword := mw.config.GetWithFallback("Email", "password", "")

	data := map[string]string{
		"email_prefix":   emailPrefix,
		"email_password": emailPassword,
	}

	response := APIResponse{
		Success: true,
		Data:    data,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleAutoLogin 处理一键登录
func (mw *MainWindow) handleAutoLogin(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	go func() {
		password := mw.config.GetWithFallback("Email", "password", "")
		prefix := mw.config.GetWithFallback("Email", "prefix", "")
		monitoringEmail := fmt.Sprintf("%<EMAIL>", prefix)
		loginEmail := web.GenerateRandomEmail(nil)

		statusCallback := func(msg string) {
			fmt.Printf("[一键登录] %s\n", msg)
		}

		if err := web.RunAutoLoginFlow(monitoringEmail, loginEmail, password, statusCallback); err != nil {
			fmt.Printf("一键登录失败: %v\n", err)
		}
	}()

	response := APIResponse{
		Success: true,
		Message: "一键登录已启动，请查看控制台输出",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleResetEnv 处理环境重置
func (mw *MainWindow) handleResetEnv(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	go func() {
		statusCallback := func(msg string) {
			fmt.Printf("[环境重置] %s\n", msg)
		}

		if err := app.RunFullResetFlow(statusCallback); err != nil {
			fmt.Printf("环境重置失败: %v\n", err)
		}
	}()

	response := APIResponse{
		Success: true,
		Message: "环境重置已启动，请查看控制台输出",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleEmailClient 处理邮箱客户端
func (mw *MainWindow) handleEmailClient(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	prefix := mw.config.GetWithFallback("Email", "prefix", "")
	password := mw.config.GetWithFallback("Email", "password", "")

	if prefix == "" || password == "" {
		response := APIResponse{
			Success: false,
			Message: "请先设置邮箱凭据",
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}

	emailToWatch := fmt.Sprintf("%<EMAIL>", prefix)

	go func() {
		statusCallback := func(msg string) {
			if strings.Contains(msg, "VERIFICATION_CODE:") {
				code := strings.Split(msg, "VERIFICATION_CODE:")[1]
				fmt.Printf("[邮箱客户端] 验证码: %s\n", strings.TrimSpace(code))
			} else {
				fmt.Printf("[邮箱客户端] %s\n", msg)
			}
		}

		if err := email.LaunchEmailClientProcess(emailToWatch, password, statusCallback); err != nil {
			fmt.Printf("邮箱客户端启动失败: %v\n", err)
		}
	}()

	response := APIResponse{
		Success: true,
		Message: "邮箱客户端已启动，请查看控制台输出",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleSaveCredentials 处理保存凭据
func (mw *MainWindow) handleSaveCredentials(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Prefix   string `json:"prefix"`
		Password string `json:"password"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		response := APIResponse{
			Success: false,
			Message: "请求格式错误",
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}

	mw.config.Set("Email", "prefix", strings.TrimSpace(req.Prefix))
	mw.config.Set("Email", "password", req.Password)

	var message string
	var success bool
	if err := mw.config.Save(); err != nil {
		message = "保存凭据失败"
		success = false
	} else {
		message = "凭据已保存"
		success = true
	}

	response := APIResponse{
		Success: success,
		Message: message,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGenerateEmail 处理生成邮箱
func (mw *MainWindow) handleGenerateEmail(w http.ResponseWriter, r *http.Request) {
	email := web.GenerateRandomEmail(nil)

	data := map[string]string{
		"email": email,
	}

	response := APIResponse{
		Success: true,
		Data:    data,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// openBrowser 打开浏览器
func (mw *MainWindow) openBrowser(url string) {
	var cmd string
	var args []string

	switch runtime.GOOS {
	case "windows":
		cmd = "cmd"
		args = []string{"/c", "start", url}
	case "darwin":
		cmd = "open"
		args = []string{url}
	default: // linux
		cmd = "xdg-open"
		args = []string{url}
	}

	exec.Command(cmd, args...).Start()
}

// Run 运行应用程序
func (mw *MainWindow) Run() {
	fmt.Println("🚀 Cursor管理工具已启动")
	fmt.Println("📱 Web界面地址: http://localhost:8080")
	fmt.Println("💡 浏览器应该会自动打开，如果没有请手动访问上述地址")
	fmt.Println("🛑 按 Ctrl+C 退出程序")

	// 保持程序运行
	select {}
}
