# Cursor管理工具 - Go版本构建脚本

# 变量定义
BINARY_NAME=cursor-manager
BINARY_WINDOWS=$(BINARY_NAME).exe
BINARY_LINUX=$(BINARY_NAME)
BINARY_DARWIN=$(BINARY_NAME)

# Go相关变量
GO_CMD=go
GO_BUILD=$(GO_CMD) build
GO_CLEAN=$(GO_CMD) clean
GO_TEST=$(GO_CMD) test
GO_GET=$(GO_CMD) get
GO_MOD=$(GO_CMD) mod

# 构建目录
BUILD_DIR=build
MAIN_PATH=./cmd/cursor-manager

# 默认目标
.PHONY: all build clean test deps help

all: clean deps build

# 安装依赖
deps:
	@echo "正在安装依赖..."
	$(GO_MOD) download
	$(GO_MOD) tidy

# 构建当前平台版本
build:
	@echo "正在构建当前平台版本..."
	@mkdir -p $(BUILD_DIR)
	$(GO_BUILD) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# 构建Windows版本
build-windows:
	@echo "正在构建Windows版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GO_BUILD) -o $(BUILD_DIR)/$(BINARY_WINDOWS) $(MAIN_PATH)

# 构建Linux版本
build-linux:
	@echo "正在构建Linux版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GO_BUILD) -o $(BUILD_DIR)/$(BINARY_LINUX) $(MAIN_PATH)

# 构建macOS版本
build-darwin:
	@echo "正在构建macOS版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 $(GO_BUILD) -o $(BUILD_DIR)/$(BINARY_DARWIN) $(MAIN_PATH)

# 构建所有平台版本
build-all: build-windows build-linux build-darwin

# 运行测试
test:
	@echo "正在运行测试..."
	$(GO_TEST) -v ./...

# 运行程序
run:
	@echo "正在运行程序..."
	$(GO_CMD) run $(MAIN_PATH)

# 清理构建文件
clean:
	@echo "正在清理构建文件..."
	$(GO_CLEAN)
	@rm -rf $(BUILD_DIR)

# 格式化代码
fmt:
	@echo "正在格式化代码..."
	$(GO_CMD) fmt ./...

# 代码检查
vet:
	@echo "正在进行代码检查..."
	$(GO_CMD) vet ./...

# 安装到系统
install: build
	@echo "正在安装到系统..."
	@cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/

# 显示帮助信息
help:
	@echo "可用的构建目标:"
	@echo "  all          - 清理、安装依赖并构建"
	@echo "  build        - 构建当前平台版本"
	@echo "  build-windows- 构建Windows版本"
	@echo "  build-linux  - 构建Linux版本"
	@echo "  build-darwin - 构建macOS版本"
	@echo "  build-all    - 构建所有平台版本"
	@echo "  test         - 运行测试"
	@echo "  run          - 运行程序"
	@echo "  clean        - 清理构建文件"
	@echo "  fmt          - 格式化代码"
	@echo "  vet          - 代码检查"
	@echo "  deps         - 安装依赖"
	@echo "  install      - 安装到系统"
	@echo "  help         - 显示此帮助信息"
