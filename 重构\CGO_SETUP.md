# CGO编译环境设置指南

本项目需要CGO支持来编译SQLite数据库和Fyne GUI组件。以下是各平台的CGO编译器安装指南。

## Windows平台

### 方法1: 安装TDM-GCC（推荐）

1. 访问 [TDM-GCC官网](https://jmeubank.github.io/tdm-gcc/)
2. 下载并安装TDM-GCC
3. 安装完成后，GCC会自动添加到PATH环境变量

### 方法2: 安装MinGW-w64

1. 访问 [MinGW-w64官网](https://www.mingw-w64.org/downloads/)
2. 下载MSYS2安装包
3. 安装MSYS2后，在MSYS2终端中运行：
   ```bash
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-pkg-config
   ```
4. 将MinGW-w64的bin目录添加到PATH环境变量

### 方法3: 使用Chocolatey

如果已安装Chocolatey包管理器：
```cmd
choco install mingw
```

### 验证安装

在命令提示符或PowerShell中运行：
```cmd
gcc --version
```

如果显示GCC版本信息，说明安装成功。

## macOS平台

### 安装Xcode Command Line Tools

```bash
xcode-select --install
```

### 验证安装

```bash
gcc --version
```

## Linux平台

### Ubuntu/Debian

```bash
sudo apt update
sudo apt install build-essential
```

### CentOS/RHEL/Fedora

```bash
# CentOS/RHEL
sudo yum groupinstall "Development Tools"

# Fedora
sudo dnf groupinstall "Development Tools"
```

### Arch Linux

```bash
sudo pacman -S base-devel
```

## 构建项目

安装CGO编译器后，可以使用以下命令构建项目：

### Windows

```cmd
# 使用批处理脚本
build.bat

# 或手动构建
set CGO_ENABLED=1
go build -o build\cursor-manager.exe .\cmd\cursor-manager
```

### macOS/Linux

```bash
# 使用Makefile
make build

# 或手动构建
CGO_ENABLED=1 go build -o build/cursor-manager ./cmd/cursor-manager
```

## 常见问题

### 问题1: "gcc not found"

**解决方案**: 确保GCC已正确安装并添加到PATH环境变量中。

### 问题2: "cgo: C compiler not found"

**解决方案**: 
1. 重启命令提示符/终端
2. 检查PATH环境变量是否包含GCC路径
3. 尝试重新安装GCC

### 问题3: Windows上的链接错误

**解决方案**: 
1. 确保使用64位版本的MinGW-w64
2. 检查是否有多个GCC版本冲突
3. 尝试使用TDM-GCC替代MinGW

### 问题4: macOS上的权限问题

**解决方案**: 
```bash
sudo xcode-select --reset
xcode-select --install
```

## 无CGO替代方案

如果无法安装CGO编译器，可以考虑：

1. **使用预编译版本**: 从项目发布页面下载预编译的二进制文件
2. **Docker构建**: 使用包含CGO环境的Docker镜像进行构建
3. **云端构建**: 使用GitHub Actions等CI/CD服务进行构建

## 技术支持

如果在安装过程中遇到问题，请：

1. 检查系统架构（32位/64位）
2. 确认Go版本兼容性
3. 查看详细的错误日志
4. 参考Go官方CGO文档：https://golang.org/cmd/cgo/
